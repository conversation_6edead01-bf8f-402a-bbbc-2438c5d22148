import os
import numpy as np
import pandas as pd


def convert_csv_to_npy(csv_folder, npy_folder):
    # 如果npy文件夹不存在，则创建
    if not os.path.exists(npy_folder):
        os.makedirs(npy_folder)

    # 获取csv文件夹中的所有文件
    csv_files = [f for f in os.listdir(csv_folder) if f.endswith('.csv')]

    for csv_file in csv_files:
        # 构造csv文件路径
        csv_path = os.path.join(csv_folder, csv_file)

        # 读取csv文件
        data = pd.read_csv(csv_path)

        # 转换为numpy数组
        npy_data = data.to_numpy()

        # 构造npy文件路径
        npy_file = os.path.splitext(csv_file)[0] + '.npy'
        npy_path = os.path.join(npy_folder, npy_file)

        # 保存为npy文件
        np.save(npy_path, npy_data)

        print(f"转换 {csv_file} 为 {npy_file}")


# 使用示例
csv_folder = '2024.12.4测试_删除列'  # csv文件夹路径
npy_folder = '2024.12.4测试_删除列_npy'  # npy文件夹路径
convert_csv_to_npy(csv_folder, npy_folder)
