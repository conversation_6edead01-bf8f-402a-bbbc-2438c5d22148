import pandas as pd
from sklearn.metrics import precision_score, accuracy_score, recall_score
from datetime import timedelta

# 读取数据
df = pd.read_excel('anomaly_results.xlsx')

# 计算精确率、准确率、召回率
y_true = df['True_Label']  # 真实标签
y_pred = df['Predicted_Anomaly']  # 预测值

precision = precision_score(y_true, y_pred)
accuracy = accuracy_score(y_true, y_pred)
recall = recall_score(y_true, y_pred)

print(f"精确率: {precision:.2f}")
print(f"准确率: {accuracy:.2f}")
print(f"召回率: {recall:.2f}")

# 2. 打印出预测值连续为1的时间段
def find_continuous_ones(df):
    df['timestamp'] = pd.to_datetime(df['Timestamp'])  # 转换时间戳格式
    continuous_ones = []
    start_idx = None

    for i in range(1, len(df)):
        if df['Predicted_Anomaly'].iloc[i] == 1 and (df['Predicted_Anomaly'].iloc[i-1] == 1):
            continue
        if df['Predicted_Anomaly'].iloc[i] == 1 and df['Predicted_Anomaly'].iloc[i-1] == 0:
            start_idx = i

        if df['Predicted_Anomaly'].iloc[i] == 0 and start_idx is not None:
            end_idx = i-1
            duration = (df['timestamp'].iloc[end_idx] - df['timestamp'].iloc[start_idx]).total_seconds() / 60  # 持续时间以分钟计算
            continuous_ones.append({
                'start_time': df['timestamp'].iloc[start_idx],
                'end_time': df['timestamp'].iloc[end_idx],
                'length': end_idx - start_idx + 1,
                'duration_min': duration
            })
            start_idx = None

    # 如果最后一段是连续的1
    if start_idx is not None:
        end_idx = len(df) - 1
        duration = (df['timestamp'].iloc[end_idx] - df['timestamp'].iloc[start_idx]).total_seconds() / 60
        continuous_ones.append({
            'start_time': df['timestamp'].iloc[start_idx],
            'end_time': df['timestamp'].iloc[end_idx],
            'length': end_idx - start_idx + 1,
            'duration_min': duration
        })

    return continuous_ones

continuous_ones = find_continuous_ones(df)
for segment in continuous_ones:
    print(f"开始时间: {segment['start_time']}, 结束时间: {segment['end_time']}, 连续1的个数: {segment['length']}, 持续时间: {segment['duration_min']:.2f} 分钟")

# 3. 打印出高风险预警时间（连续5分钟预测为1）
def high_risk_warning(df, time_window_minutes=5):
    df['timestamp'] = pd.to_datetime(df['Timestamp'])
    high_risk_periods = []
    start_idx = None

    for i in range(len(df)):
        # 如果预测值为1，并且还在同一个高风险段内
        if df['Predicted_Anomaly'].iloc[i] == 1:
            if start_idx is None:  # 找到一个新的高风险段开始
                start_idx = i
        else:
            if start_idx is not None:  # 结束一个高风险段
                duration = (df['timestamp'].iloc[i-1] - df['timestamp'].iloc[start_idx]).total_seconds() / 60
                if duration >= time_window_minutes:  # 如果持续时间超过5分钟
                    high_risk_periods.append({
                        'start_time': df['timestamp'].iloc[start_idx],
                        'end_time': df['timestamp'].iloc[i-1],
                        'risk_duration_min': duration
                    })
                start_idx = None

    # 处理最后一个高风险段
    if start_idx is not None:
        end_idx = len(df) - 1
        duration = (df['timestamp'].iloc[end_idx] - df['timestamp'].iloc[start_idx]).total_seconds() / 60
        if duration >= time_window_minutes:
            high_risk_periods.append({
                'start_time': df['timestamp'].iloc[start_idx],
                'end_time': df['timestamp'].iloc[end_idx],
                'risk_duration_min': duration
            })

    return high_risk_periods



# 打印高风险预警时段
high_risk_periods = high_risk_warning(df)
for risk in high_risk_periods:
    print(f"高风险预警开始时间: {risk['start_time']}, 结束时间: {risk['end_time']}, 持续时间: {risk['risk_duration_min']} 分钟")

