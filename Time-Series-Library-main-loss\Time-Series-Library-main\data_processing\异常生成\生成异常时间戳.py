import os
import pandas as pd
import chardet

# 输入CSV文件路径
input_file_path = os.path.join('原始', '宁216H2-2(1).csv')

# 检测文件编码
with open(input_file_path, 'rb') as f:
    result = chardet.detect(f.read(10000))
encoding = result['encoding']

# 读取CSV文件
df = pd.read_csv(input_file_path, encoding=encoding)

# 打印DataFrame的列名
print("DataFrame的列名：", df.columns)

# 确保`sfkz`和`date`列存在
if 'sfkz' in df.columns and 'date' in df.columns:
    # 确保 `date` 列为时间格式
    df['date'] = pd.to_datetime(df['date'])

    # 初始化变量
    in_sequence = False
    start_index = 0
    sequence_length = 0
    sequences = []
    high_risk_sequences = []  # 用于保存高风险预警的时间段

    # 遍历DataFrame的每一行
    for i, row in df.iterrows():
        if row['sfkz'] == 1:
            if not in_sequence:
                in_sequence = True
                start_index = i
            sequence_length += 1
        else:
            if in_sequence:
                in_sequence = False
                end_index = i - 1
                start_date = df.loc[start_index, 'date']
                end_date = df.loc[end_index, 'date']
                # 计算持续时间，时间差值以分钟为单位
                duration = (end_date - start_date).total_seconds() / 60  # 持续时间，单位为分钟
                sequences.append((sequence_length, start_date, end_date, duration))

                # 如果持续时间大于1分钟（30个1值），则添加到高风险预警列表中
                if duration > 5:  # 这里的条件是持续时间大于1分钟
                    high_risk_sequences.append((sequence_length, start_date, end_date, duration))

                sequence_length = 0

    # 处理最后的序列
    if in_sequence:
        end_index = len(df) - 1
        start_date = df.loc[start_index, 'date']
        end_date = df.loc[end_index, 'date']
        duration = (end_date - start_date).total_seconds() / 60  # 持续时间，单位为分钟
        sequences.append((sequence_length, start_date, end_date, duration))

        # 如果持续时间大于1分钟（30个1值），则添加到高风险预警列表中
        if duration > 1:
            high_risk_sequences.append((sequence_length, start_date, end_date, duration))

    # 输出所有序列的结果
    for length, start_date, end_date, duration in sequences:
        print(f"连续1值的个数: {length}, 开始时间戳: {start_date}, 结束时间戳: {end_date}, 持续时间: {duration:.2f} 分钟")

    # 如果存在高风险预警
    if high_risk_sequences:
        print("\n高风险预警的时间段：")
        for length, start_date, end_date, duration in high_risk_sequences:
            print(f"高风险预警 - 连续1值的个数: {length}, 开始时间戳: {start_date}, 结束时间戳: {end_date}, 持续时间: {duration:.2f} 分钟")
    else:
        # 如果没有高风险预警，输出最长的连续1值时间段
        if sequences:
            max_sequence = max(sequences, key=lambda x: x[0])
            length, start_date, end_date, duration = max_sequence
            print("\n没有高风险预警")
            # print(f"最长连续1值 - 连续1值的个数: {length}, 开始时间戳: {start_date}, 结束时间戳: {end_date}, 持续时间: {duration:.2f} 分钟")
        else:
            print("\n没有连续1值的时间段。")

else:
    print("CSV文件中缺少`sfkz`或`date`列")



# import os
# import pandas as pd
# import chardet
#
# # 输入CSV文件路径
# input_file_path = os.path.join('原始', '宁209H20-3实时数据.csv')
#
# # 检测文件编码
# with open(input_file_path, 'rb') as f:
#     result = chardet.detect(f.read(10000))
# encoding = result['encoding']
#
# # 读取CSV文件
# df = pd.read_csv(input_file_path, encoding=encoding)
#
# # 打印DataFrame的列名
# print("DataFrame的列名：", df.columns)
#
# # 确保`sfkz`和`date`列存在
# if 'sfkz' in df.columns and 'date' in df.columns:
#     # 确保 `date` 列为时间格式
#     df['date'] = pd.to_datetime(df['date'])
#
#     # 初始化变量
#     in_sequence = False
#     start_index = 0
#     sequence_length = 0
#     sequences = []
#     high_risk_sequences = []  # 用于保存高风险预警的时间段
#
#     # 遍历DataFrame的每一行
#     for i, row in df.iterrows():
#         if row['sfkz'] == 1:
#             if not in_sequence:
#                 in_sequence = True
#                 start_index = i
#             sequence_length += 1
#         else:
#             if in_sequence:
#                 in_sequence = False
#                 end_index = i - 1
#                 start_date = df.loc[start_index, 'date']
#                 end_date = df.loc[end_index, 'date']
#                 # 计算持续时间，30个1等于1分钟
#                 duration = sequence_length / 30  # 持续时间，单位为分钟
#                 sequences.append((sequence_length, start_date, end_date, duration))
#
#                 # 如果持续时间大于1分钟（30个1值），则添加到高风险预警列表中
#                 if sequence_length > 30:  # 这里的条件是超过1分钟，即超过30个1
#                     high_risk_sequences.append((sequence_length, start_date, end_date, duration))
#
#                 sequence_length = 0
#
#     # 处理最后的序列
#     if in_sequence:
#         end_index = len(df) - 1
#         start_date = df.loc[start_index, 'date']
#         end_date = df.loc[end_index, 'date']
#         duration = sequence_length / 30  # 持续时间，单位为分钟
#         sequences.append((sequence_length, start_date, end_date, duration))
#
#         # 如果持续时间大于1分钟（30个1值），则添加到高风险预警列表中
#         if sequence_length > 30:
#             high_risk_sequences.append((sequence_length, start_date, end_date, duration))
#
#     # 输出所有序列的结果
#     for length, start_date, end_date, duration in sequences:
#         print(f"连续1值的个数: {length}, 开始时间戳: {start_date}, 结束时间戳: {end_date}, 持续时间: {duration:.2f} 分钟")
#
#     # 如果存在高风险预警
#     if high_risk_sequences:
#         print("\n高风险预警（连续1值大于1分钟）的时间段：")
#         for length, start_date, end_date, duration in high_risk_sequences:
#             print(f"高风险预警 - 连续1值的个数: {length}, 开始时间戳: {start_date}, 结束时间戳: {end_date}, 持续时间: {duration:.2f} 分钟")
#     else:
#         # 如果没有高风险预警，输出最长的连续1值时间段
#         if sequences:
#             max_sequence = max(sequences, key=lambda x: x[0])
#             length, start_date, end_date, duration = max_sequence
#             print("\n没有高风险预警，输出最长的连续1值时间段作为高风险预警：")
#             print(f"最长连续1值 - 连续1值的个数: {length}, 开始时间戳: {start_date}, 结束时间戳: {end_date}, 持续时间: {duration:.2f} 分钟")
#         else:
#             print("\n没有连续1值的时间段。")
#
# else:
#     print("CSV文件中缺少`sfkz`或`date`列")
