from data_provider.data_factory import data_provider
from exp.exp_basic import Exp_Basic
from utils.tools import EarlyStopping, adjust_learning_rate, adjustment
from sklearn.metrics import precision_recall_fscore_support
from sklearn.metrics import accuracy_score
import torch.multiprocessing
import pandas as pd
from utils.stuck_pipe_detector import StuckPipeDetector  # 导入我们的检测器
import time
import os
import csv
import json

# 添加全局变量，用于记录所有测试文件的异常比例
ANOMALY_RATIO_SUMMARY = {}
SUMMARY_FILE = "卡钻异常比例汇总.csv"

torch.multiprocessing.set_sharing_strategy('file_system')
import torch
import torch.nn as nn
from torch import optim
import os
import time
import warnings
import numpy as np

warnings.filterwarnings('ignore')


class Exp_Anomaly_Detection(Exp_Basic):
    # 添加静态缓存变量用于存储训练集重建误差
    _train_energy_cache = None
    _model_cache_id = None
    
    def __init__(self, args):
        super(Exp_Anomaly_Detection, self).__init__(args)

    def _build_model(self):
        model = self.model_dict[self.args.model].Model(self.args).float()

        if self.args.use_multi_gpu and self.args.use_gpu:
            model = nn.DataParallel(model, device_ids=self.args.device_ids)
        return model

    def _get_data(self, flag):
        data_set, data_loader = data_provider(self.args, flag)
        return data_set, data_loader

    def _select_optimizer(self):
        model_optim = optim.Adam(self.model.parameters(), lr=self.args.learning_rate)
        return model_optim

    def _select_criterion(self):
        criterion = nn.MSELoss()
        return criterion

    def vali(self, vali_data, vali_loader, criterion):
        total_loss = []
        self.model.eval()
        with torch.no_grad():
            for i, (batch_x, _, _) in enumerate(vali_loader):
                batch_x = batch_x.float().to(self.device)

                outputs = self.model(batch_x, None, None, None)

                f_dim = -1 if self.args.features == 'MS' else 0
                outputs = outputs[:, :, f_dim:]
                pred = outputs.detach().cpu()
                true = batch_x.detach().cpu()

                loss = criterion(pred, true)
                total_loss.append(loss)
        total_loss = np.average(total_loss)
        self.model.train()
        return total_loss

    def _calculate_train_energy(self, train_loader):
        """计算训练集重建误差"""
        attens_energy = []
        with torch.no_grad():
            for i, (batch_x, batch_y) in enumerate(train_loader):
                batch_x = batch_x.float().to(self.device)
                # reconstruction
                outputs = self.model(batch_x, None, None, None)
                # criterion
                score = torch.mean(self.anomaly_criterion(batch_x, outputs), dim=-1)
                score = score.detach().cpu().numpy()
                attens_energy.append(score)

        attens_energy = np.concatenate(attens_energy, axis=0).reshape(-1)
        return np.array(attens_energy)

    def train(self, setting):
        train_data, train_loader = self._get_data(flag='train')
        vali_data, vali_loader = self._get_data(flag='val')
        test_data, test_loader = self._get_data(flag='test')

        path = os.path.join(self.args.checkpoints, setting)
        if not os.path.exists(path):
            os.makedirs(path)

        time_now = time.time()

        train_steps = len(train_loader)
        early_stopping = EarlyStopping(patience=self.args.patience, verbose=True)

        model_optim = self._select_optimizer()
        criterion = self._select_criterion()

        for epoch in range(self.args.train_epochs):
            iter_count = 0
            train_loss = []

            self.model.train()
            epoch_time = time.time()
            for i, (batch_x, batch_y) in enumerate(train_loader):
                iter_count += 1
                model_optim.zero_grad()

                batch_x = batch_x.float().to(self.device)

                outputs = self.model(batch_x, None, None, None)

                f_dim = -1 if self.args.features == 'MS' else 0
                outputs = outputs[:, :, f_dim:]
                loss = criterion(outputs, batch_x)
                train_loss.append(loss.item())

                if (i + 1) % 100 == 0:
                    print("\titers: {0}, epoch: {1} | loss: {2:.7f}".format(i + 1, epoch + 1, loss.item()))
                    speed = (time.time() - time_now) / iter_count
                    left_time = speed * ((self.args.train_epochs - epoch) * train_steps - i)
                    print('\tspeed: {:.4f}s/iter; left time: {:.4f}s'.format(speed, left_time))
                    iter_count = 0
                    time_now = time.time()

                loss.backward()
                model_optim.step()

            print("Epoch: {} cost time: {}".format(epoch + 1, time.time() - epoch_time))
            train_loss = np.average(train_loss)
            vali_loss = self.vali(vali_data, vali_loader, criterion)
            test_loss = self.vali(test_data, test_loader, criterion)

            print("Epoch: {0}, Steps: {1} | Train Loss: {2:.7f} Vali Loss: {3:.7f} Test Loss: {4:.7f}".format(
                epoch + 1, train_steps, train_loss, vali_loss, test_loss))
            early_stopping(vali_loss, self.model, path)
            if early_stopping.early_stop:
                print("Early stopping")
                break
            adjust_learning_rate(model_optim, epoch + 1, self.args)

        best_model_path = path + '/' + 'checkpoint.pth'
        self.model.load_state_dict(torch.load(best_model_path))

        return self.model

    def _save_anomaly_ratio_summary(self):
        """保存所有异常比例汇总到CSV文件"""
        global ANOMALY_RATIO_SUMMARY, SUMMARY_FILE
        
        # 如果汇总文件已存在，尝试先读取已有数据
        existing_data = {}
        if os.path.exists(SUMMARY_FILE):
            try:
                with open(SUMMARY_FILE, 'r', newline='', encoding='utf-8-sig') as f:
                    reader = csv.reader(f)
                    headers = next(reader)  # 跳过表头
                    for row in reader:
                        if len(row) >= 3:
                            file_name = row[0]
                            ratio = row[1]
                            is_valid = row[2]
                            existing_data[file_name] = {
                                'ratio': ratio,
                                'valid': True if is_valid == "是" else False
                            }
            except Exception as e:
                print(f"读取已有汇总文件失败: {e}")
                # 如果读取失败，尝试不同的编码
                try:
                    with open(SUMMARY_FILE, 'r', newline='', encoding='gbk') as f:
                        reader = csv.reader(f)
                        headers = next(reader)  # 跳过表头
                        for row in reader:
                            if len(row) >= 3:
                                file_name = row[0]
                                ratio = row[1]
                                is_valid = row[2]
                                existing_data[file_name] = {
                                    'ratio': ratio,
                                    'valid': True if is_valid == "是" else False
                                }
                    print("成功使用GBK编码读取文件")
                except Exception as e2:
                    print(f"使用GBK编码读取也失败: {e2}，将创建新文件")
        
        # 合并已有数据和新数据
        merged_data = {**existing_data, **ANOMALY_RATIO_SUMMARY}
        
        # 写入CSV文件，明确使用UTF-8编码
        try:
            with open(SUMMARY_FILE, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(['文件名', '经验卡钻检测异常比例(%)', '是否使用经验卡钻检测'])
                
                for file_name, data in merged_data.items():
                    ratio = data.get('ratio', 'N/A')
                    is_valid = data.get('valid', False)
                    if ratio != 'N/A':
                        writer.writerow([file_name, f"{ratio:.2f}" if isinstance(ratio, float) else ratio, "是" if is_valid else "否"])
                    else:
                        writer.writerow([file_name, "N/A", "否"])
            
            print(f"异常比例汇总已保存到 {SUMMARY_FILE}")
            
            # 同时保存一份JSON格式的数据，便于其他程序读取
            with open(SUMMARY_FILE.replace('.csv', '.json'), 'w', encoding='utf-8') as f:
                json.dump(merged_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存汇总文件失败: {e}")
            # 如果保存失败，尝试使用临时文件名
            try:
                tmp_file = f"卡钻异常比例汇总_{int(time.time())}.csv"
                with open(tmp_file, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(['文件名', '经验卡钻检测异常比例(%)', '是否使用经验卡钻检测'])
                    
                    for file_name, data in merged_data.items():
                        ratio = data.get('ratio', 'N/A')
                        is_valid = data.get('valid', False)
                        if ratio != 'N/A':
                            writer.writerow([file_name, f"{ratio:.2f}" if isinstance(ratio, float) else ratio, "是" if is_valid else "否"])
                        else:
                            writer.writerow([file_name, "N/A", "否"])
                
                print(f"异常比例汇总已保存到临时文件 {tmp_file}")
            except Exception as e2:
                print(f"保存到临时文件也失败: {e2}")

    def test(self, setting, test=0):
        # 记录总体开始时间
        total_start_time = time.time()
        
        # 获取测试文件名
        test_file_name = getattr(self.args, 'test_file', 'unknown')
        test_file_base = os.path.splitext(test_file_name)[0] if test_file_name else 'unknown'
        
        # 1. 数据加载阶段
        data_loading_start = time.time()
        test_data, test_loader = self._get_data(flag='test')
        train_data, train_loader = self._get_data(flag='train')
        data_loading_time = time.time() - data_loading_start
        print(f"[性能统计] 数据加载耗时: {data_loading_time:.2f}秒")
        
        if test:
            model_loading_start = time.time()
            print('loading model')
            self.model.load_state_dict(torch.load(os.path.join('./checkpoints/' + setting, 'checkpoint.pth')))
            print(f"[性能统计] 模型加载耗时: {time.time() - model_loading_start:.2f}秒")

        attens_energy = []
        folder_path = './test_results/' + setting + '/'
        if not os.path.exists(folder_path):
            os.makedirs(folder_path)

        self.model.eval()
        self.anomaly_criterion = nn.MSELoss(reduce=False)

        # 检查是否为无标签模式
        no_label_mode = getattr(self.args, 'no_label', False)
        if no_label_mode:
            print("无标签模式: 仅进行预测，不评估性能指标")

        # 2. 训练集重建误差计算
        train_error_start = time.time()
        
        # 检查缓存是否有效：检查模型ID是否一致
        current_model_id = f"{setting}_{self.args.model}"
        cache_path = self._get_cache_path(current_model_id)
        
        # 检查磁盘缓存是否存在
        if os.path.exists(cache_path):
            # 使用磁盘缓存的训练集重建误差
            print(f"[性能优化] 使用磁盘缓存的训练集重建误差，跳过重新计算")
            try:
                train_energy = np.load(cache_path)
                print(f"[性能优化] 成功从磁盘加载缓存: {cache_path}")
            except Exception as e:
                print(f"[性能优化] 读取缓存失败: {e}，将重新计算")
                # 缓存读取失败，重新计算
                train_energy = self._calculate_train_energy(train_loader)
                # 保存到磁盘
                try:
                    np.save(cache_path, train_energy)
                    print(f"[性能优化] 已将训练集重建误差保存到: {cache_path}")
                except Exception as e:
                    print(f"[性能优化] 缓存保存失败: {e}")
        else:
            # 首次计算训练集重建误差
            print(f"[性能优化] 首次计算训练集重建误差并缓存")
            train_energy = self._calculate_train_energy(train_loader)
            # 保存到磁盘
            try:
                np.save(cache_path, train_energy)
                print(f"[性能优化] 已将训练集重建误差保存到: {cache_path}")
            except Exception as e:
                print(f"[性能优化] 缓存保存失败: {e}")
        
        train_error_time = time.time() - train_error_start
        print(f"[性能统计] 训练集重建误差计算耗时: {train_error_time:.2f}秒")

        # 3. 测试集重建误差计算
        test_error_start = time.time()
        attens_energy = []
        test_labels = []
        test_timestamps = []
        raw_test_data = []

        for i, (batch_x, batch_y, test_timestamp) in enumerate(test_loader):
            batch_x = batch_x.float().to(self.device)
            # 保存原始数据用于专业卡钻检测
            raw_test_data.append(batch_x.cpu().numpy())

            # reconstruction
            outputs = self.model(batch_x, None, None, None)
            # criterion
            score = torch.mean(self.anomaly_criterion(batch_x, outputs), dim=-1)
            score = score.detach().cpu().numpy()
            attens_energy.append(score)
            test_labels.append(batch_y)
            test_timestamp = test_timestamp.numpy()
            test_timestamp = test_timestamp.astype('datetime64[s]')
            test_timestamps.append(test_timestamp)

        attens_energy = np.concatenate(attens_energy, axis=0).reshape(-1)
        test_energy = np.array(attens_energy)
        test_error_time = time.time() - test_error_start
        print(f"[性能统计] 测试集重建误差计算耗时: {test_error_time:.2f}秒")

        # 4. 阈值计算
        threshold_start = time.time()
        combined_energy = np.concatenate([train_energy, test_energy], axis=0)
        threshold = np.percentile(combined_energy, 100 - self.args.anomaly_ratio)
        print("重建误差阈值:", threshold)
        threshold_time = time.time() - threshold_start
        print(f"[性能统计] 阈值计算耗时: {threshold_time:.2f}秒")

        # 5. 专业卡钻检测或阈值方法
        anomaly_detect_start = time.time()
        # 首先尝试使用专业卡钻检测方法
        # 直接在代码中设置控制开关，不再依赖环境变量或命令行参数
        use_stuck_pipe_detector = True  # 设置为False关闭专业卡钻检测功能，True则启用
        
        print(f"专业卡钻检测状态: {'启用' if use_stuck_pipe_detector else '关闭'}")
        
        if use_stuck_pipe_detector:
            try:
                # 合并所有批次的测试数据
                if raw_test_data:
                    raw_test_data = np.concatenate(raw_test_data, axis=0)
                    print(f"专业卡钻检测数据形状: {raw_test_data.shape}")

                    # 初始化卡钻检测器，启用GPU加速
                    detector = StuckPipeDetector(use_gpu=True)

                    # 找出扭矩和大钩负荷的索引（可根据实际数据调整）
                    # 假设特征顺序如下: [depth, bit_depth, rpm, spp, torque, hook_load, ...]
                    torque_idx = 7  # 扭矩索引
                    hook_load_idx = 5  # 大钩负荷索引

                    # 对于实际应用，可以自动调优参数
                    if self.args.tune_params:
                        print("正在调优卡钻检测参数...")
                        detector.tune_parameters(raw_test_data, torque_idx, hook_load_idx)

                    # 执行专业卡钻检测
                    print("正在执行经验卡钻检测...")
                    anomalies, scores = detector.detect(raw_test_data, torque_idx, hook_load_idx)

                    # 如果检测到的异常太少或太多，回退到基于重建误差的方法
                    anomaly_ratio = np.mean(anomalies)
                    print(f"经验卡钻检测异常比例: {anomaly_ratio * 100:.2f}%")
                    
                    # 记录异常比例到全局变量
                    global ANOMALY_RATIO_SUMMARY
                    
                    if 0.0001 <= anomaly_ratio <= 0.45:  # 合理的异常比例范围
                        print("使用经验卡钻检测结果")
                        pred = anomalies
                        test_energy = scores  # 更新异常分数
                        # 记录异常比例（使用了经验卡钻检测结果）
                        ANOMALY_RATIO_SUMMARY[test_file_base] = {
                            'ratio': anomaly_ratio * 100,
                            'valid': True
                        }
                    else:
                        print("经验卡钻检测异常比例不合理，回退到重建误差方法")
                        pred = (test_energy > threshold).astype(int)
                        # 记录异常比例（未使用经验卡钻检测结果）
                        ANOMALY_RATIO_SUMMARY[test_file_base] = {
                            'ratio': anomaly_ratio * 100,
                            'valid': False
                        }
                else:
                    print("没有获取到原始测试数据，使用重建误差方法")
                    pred = (test_energy > threshold).astype(int)
                    # 无经验卡钻检测结果
                    ANOMALY_RATIO_SUMMARY[test_file_base] = {
                        'ratio': 'N/A',
                        'valid': False
                    }
            except Exception as e:
                print(f"经验卡钻检测失败: {e}，回退到重建误差方法")
                pred = (test_energy > threshold).astype(int)
                # 经验卡钻检测失败
                ANOMALY_RATIO_SUMMARY[test_file_base] = {
                    'ratio': 'N/A',
                    'valid': False
                }
        else:
            # 专业卡钻检测已关闭，直接使用重建误差方法
            print("专业卡钻检测功能已关闭，使用重建误差方法")
            pred = (test_energy > threshold).astype(int)
            # 专业卡钻检测已关闭
            ANOMALY_RATIO_SUMMARY[test_file_base] = {
                'ratio': 'N/A',
                'valid': False
            }

        # 保存当前汇总数据
        self._save_anomaly_ratio_summary()
        
        anomaly_detect_time = time.time() - anomaly_detect_start
        print(f"[性能统计] 异常检测耗时: {anomaly_detect_time:.2f}秒")

        # 6. 后处理和评估
        postprocess_start = time.time()
        # 继续原有评估逻辑
        test_timestamps = np.concatenate(test_timestamps, axis=0).reshape(-1)
        test_labels = np.concatenate(test_labels, axis=0).reshape(-1)
        test_labels = np.array(test_labels)
        gt = test_labels.astype(int)

        print("pred:   ", pred.shape)
        
        # 只在非无标签模式下计算评估指标
        if not no_label_mode:
            print("gt:     ", gt.shape)

            # (4) detection adjustment
            # 如果需要调整检测结果，可以取消注释下面这行
            # gt, pred = adjustment(gt, pred)

            pred = np.array(pred)
            gt = np.array(gt)
            print("pred: ", pred.shape)
            print("gt:   ", gt.shape)

            # 确保维度匹配
            if len(pred) != len(gt):
                min_len = min(len(pred), len(gt))
                pred = pred[:min_len]
                gt = gt[:min_len]
                test_energy = test_energy[:min_len]
                test_timestamps = test_timestamps[:min_len]

            pred = pred.astype(int)
            accuracy = accuracy_score(gt, pred)
            precision, recall, f_score, support = precision_recall_fscore_support(gt, pred, average='binary')
            print("Accuracy : {:0.4f}, Precision : {:0.4f}, Recall : {:0.4f}, F-score : {:0.4f} ".format(
                accuracy, precision,
                recall, f_score))

            f = open("result_anomaly_detection.txt", 'a')
            f.write(setting + "  \n")
            f.write("Accuracy : {:0.4f}, Precision : {:0.4f}, Recall : {:0.4f}, F-score : {:0.4f} ".format(
                accuracy, precision,
                recall, f_score))
            f.write('\n')
            f.write('\n')
            f.close()
        
        postprocess_time = time.time() - postprocess_start
        print(f"[性能统计] 后处理和评估耗时: {postprocess_time:.2f}秒")

        # 7. 结果保存
        save_start = time.time()
        # (5) 保存结果
        batch_size = self.args.seq_len  # 假设 seq_len 是窗口大小
        sampled_indices = np.arange(0, len(test_timestamps), batch_size)

        # 生成包含时间戳和测试文件名的唯一结果文件名
        timestamp_str = time.strftime("%Y%m%d-%H%M%S")
        test_file_name = getattr(self.args, 'test_file', 'default').replace('.npy', '').replace('.', '_')
        result_file_name = f'anomaly_results_{test_file_name}_{timestamp_str}.csv'

        # 确保pred是整数类型的0和1
        pred = pred.astype(int)
        
        # 创建结果数据框
        if no_label_mode:
            # 无标签模式下不包含真实标签列
            df = pd.DataFrame({
                'Timestamp': test_timestamps[sampled_indices],
                'Anomaly_Score': test_energy[sampled_indices],
                'Predicted_Anomaly': pred[sampled_indices].astype(int)  # 确保是整数类型
            })
        else:
            # 常规模式下包含真实标签列
            df = pd.DataFrame({
                'Timestamp': test_timestamps[sampled_indices],
                'Anomaly_Score': test_energy[sampled_indices],
                'Predicted_Anomaly': pred[sampled_indices].astype(int),  # 确保是整数类型
                'True_Label': gt[sampled_indices].astype(int)            # 同样确保真实标签也是整数
            })
            
        output_file = os.path.join(folder_path, result_file_name)
        df.to_csv(output_file, index=False, encoding='utf-8')
        print(f"预测结果保存至 {output_file}，共 {len(df)} 行")
        save_time = time.time() - save_start
        print(f"[性能统计] 结果保存耗时: {save_time:.2f}秒")
        
        # 输出总耗时统计
        total_time = time.time() - total_start_time
        print("\n[性能统计] 总耗时明细:")
        print(f"- 数据加载: {data_loading_time:.2f}秒 ({data_loading_time/total_time*100:.1f}%)")
        print(f"- 训练集重建误差: {train_error_time:.2f}秒 ({train_error_time/total_time*100:.1f}%)")
        print(f"- 测试集重建误差: {test_error_time:.2f}秒 ({test_error_time/total_time*100:.1f}%)")
        print(f"- 阈值计算: {threshold_time:.2f}秒 ({threshold_time/total_time*100:.1f}%)")
        print(f"- 异常检测: {anomaly_detect_time:.2f}秒 ({anomaly_detect_time/total_time*100:.1f}%)")
        print(f"- 后处理和评估: {postprocess_time:.2f}秒 ({postprocess_time/total_time*100:.1f}%)")
        print(f"- 结果保存: {save_time:.2f}秒 ({save_time/total_time*100:.1f}%)")
        print(f"总计: {total_time:.2f}秒")
        
        return

    def _get_cache_path(self, model_id):
        """获取缓存文件路径"""
        cache_dir = './.cache'
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)
        return os.path.join(cache_dir, f'train_energy_cache_{model_id}.npy')