import pandas as pd

# 确保使用 openpyxl 引擎读取 .xlsx 文件
file_path = '宁209H20-3实时数据.xlsx'  # 请确保文件路径正确
df = pd.read_excel(file_path, engine='openpyxl')

# 创建一个字典来替换列名
columns_map = {
    '日期': 'WELLDATE',
    '时间': 'WELLTIME',
    '井深': 'DEP',
    '钻头位置': 'BITDEP',
    '大钩负荷': 'HOKHEI',
    '迟到时间': 'DRITIME',
    '钻压': 'WOB',
    '大钩高度': 'HKLD',
    '转盘转速': 'RPM',
    '扭矩': 'TOR',
    '立压': 'SPP',
    '套压': 'CSIP'
}

# 替换列名
df = df.rename(columns=columns_map)

# 选择并按顺序排列需要保留的列
required_columns = ['WELLDATE', 'WELLTIME', 'DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP']
df = df[required_columns]

# 导出为 .csv 文件
output_file_path = '宁209H20-3实时数据.csv'
df.to_csv(output_file_path, index=False)

print(f'文件已保存为 {output_file_path}')
