import os
import pandas as pd

# 定义文件夹路径
input_folder = '原始'
output_folder = '原始'  # 这是新的文件夹路径

# 创建输出文件夹，如果不存在则创建
os.makedirs(output_folder, exist_ok=True)

# 获取输入文件夹中所有csv文件
csv_files = [f for f in os.listdir(input_folder) if f.endswith('.csv')]

# 遍历每个csv文件
for csv_file in csv_files:
    # 构建输入文件的完整路径
    input_file_path = os.path.join(input_folder, csv_file)

    # 读取csv文件
    df = pd.read_csv(input_file_path)

    # 打印列名以进行调试
    print(f"Processing file: {csv_file}")
    print("Columns:", df.columns)

    # 合并前两列，如果列名正确
    if 'WELLDATE' in df.columns and 'WELLTIME' in df.columns:
        df['date'] = df['WELLDATE'] + ' ' + df['WELLTIME']

        # 删除原来的两列
        df.drop(['WELLDATE', 'WELLTIME'], axis=1, inplace=True)

        # 重新排列列顺序
        cols = ['date'] + [col for col in df.columns if col != 'date']
        df = df[cols]

        # 构建输出文件的完整路径
        output_file_path = os.path.join(output_folder, csv_file)

        # 保存修改后的csv文件到输出文件夹
        df.to_csv(output_file_path, index=False)
    else:
        print(f"Skipping file {csv_file}: required columns not found")

print("所有文件已处理并保存到新文件夹。")
