import pandas as pd
import numpy as np
from sklearn.impute import SimpleImputer
from scipy import stats

# 读取CSV文件
df = pd.read_csv("泸203H11-4-02205.csv")

# 1. 填充缺失值
# 使用均值填充数值型特征
numeric_features = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP']
imputer = SimpleImputer(strategy='mean')
df[numeric_features] = imputer.fit_transform(df[numeric_features])

# 2. 处理异常值
# 使用Z分数方法检测异常值，设定阈值为3
for feature in numeric_features:
    z_scores = np.abs(stats.zscore(df[feature]))
    df[feature] = np.where(z_scores > 3, np.nan, df[feature])  # 将异常值设为NaN
    # 再次填充处理过的NaN值
    df[feature] = df[feature].fillna(df[feature].mean())

# 保存修正后的数据
df.to_csv("cleaned_泸203H11-4-02205.csv", index=False)
