<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;37dfeb7f-18e7-4464-88b9-48aa92aa0781&quot;,&quot;conversations&quot;:{&quot;37dfeb7f-18e7-4464-88b9-48aa92aa0781&quot;:{&quot;id&quot;:&quot;37dfeb7f-18e7-4464-88b9-48aa92aa0781&quot;,&quot;createdAtIso&quot;:&quot;2025-07-17T13:00:30.422Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-17T13:10:34.809Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;2bc0d370-a8a0-4546-8719-e9c095ad911c&quot;,&quot;request_message&quot;:&quot;我需要创建一个完整的教学指南，教别人如何使用这个卡钻检测项目来处理和测试CSV格式的钻井数据。具体要求如下：\n\n1. **数据处理流程**：\n   - 如何将原始CSV格式的钻井数据转换为项目所需的NPY格式\n   - 如何使用预处理工具处理数据并生成标签文件\n   - 数据目录结构的正确组织方式\n\n2. **测试执行步骤**：\n   - 如何配置和运行单个文件测试\n   - 如何使用批量测试功能处理多个数据文件\n   - 参数配置的详细说明和最佳实践\n\n3. **结果解读指导**：\n   - 输出文件的格式和内容说明\n   - 如何理解异常检测结果和异常分数\n   - 如何生成和解读可视化图表（类似你提到的图片结果）\n\n4. **完整示例演示**：\n   - 提供一个从原始CSV数据到最终结果的完整操作示例\n   - 包含常见问题的解决方案和故障排除指南\n\n请基于项目现有的文档和代码结构，创建一个循序渐进、易于理解的教学材料，确保初学者能够独立完成整个流程并获得有意义的测试结果。\n\n&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;我需要创建一个完整的教学指南，教别人如何使用这个卡钻检测项目来处理和测试CSV格式的钻井数据。具体要求如下：&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;1. **数据处理流程**：&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - 如何将原始CSV格式的钻井数据转换为项目所需的NPY格式&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - 如何使用预处理工具处理数据并生成标签文件&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - 数据目录结构的正确组织方式&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;2. **测试执行步骤**：&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - 如何配置和运行单个文件测试&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - 如何使用批量测试功能处理多个数据文件&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - 参数配置的详细说明和最佳实践&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;3. **结果解读指导**：&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - 输出文件的格式和内容说明&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - 如何理解异常检测结果和异常分数&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - 如何生成和解读可视化图表（类似你提到的图片结果）&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;4. **完整示例演示**：&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - 提供一个从原始CSV数据到最终结果的完整操作示例&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;   - 包含常见问题的解决方案和故障排除指南&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;请基于项目现有的文档和代码结构，创建一个循序渐进、易于理解的教学材料，确保初学者能够独立完成整个流程并获得有意义的测试结果。&quot;}]},{&quot;type&quot;:&quot;file&quot;,&quot;attrs&quot;:{&quot;src&quot;:&quot;1f443beecc462c62dbb510b41281a805a9ed28e5093ebf41c5270f212c57e9cd.png&quot;,&quot;alt&quot;:null,&quot;title&quot;:&quot;image.png&quot;,&quot;renderMode&quot;:&quot;collapsed&quot;,&quot;isLoading&quot;:false,&quot;fileSizeBytes&quot;:27256,&quot;mimeType&quot;:&quot;image/png&quot;}}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_015MsQVHTKawjxZbz7GNu2eJ&quot;,&quot;tool_name&quot;:&quot;get-memory-bank-info_memory-bank&quot;,&quot;input_json&quot;:&quot;{\&quot;rootPath\&quot;: \&quot;D:/0temp/\\u7b97\\u6cd5/\\u5361\\u94bb\\u4ee3\\u7801label\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;memory-bank&quot;,&quot;mcp_tool_name&quot;:&quot;get-memory-bank-info&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;## 三维评估\n\n**理解深度**：高 - 需要深入理解项目架构、数据处理流程和教学材料设计\n**变更范围**：系统 - 需要创建完整的教学指南，涉及多个模块和流程\n**风险等级**：低 - 创建文档材料，不涉及代码修改\n\n基于评估结果，采用**协作规划模式**进行处理。\n\n让我首先获取项目的完整信息，然后制定详细的教学指南创建计划。\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;我需要创建一个完整的教学指南，教别人如何使用这个卡钻检测项目来处理和测试CSV格式的钻井数据。具体要求如下：\n\n1. **数据处理流程**：\n   - 如何将原始CSV格式的钻井数据转换为项目所需的NPY格式\n   - 如何使用预处理工具处理数据并生成标签文件\n   - 数据目录结构的正确组织方式\n\n2. **测试执行步骤**：\n   - 如何配置和运行单个文件测试\n   - 如何使用批量测试功能处理多个数据文件\n   - 参数配置的详细说明和最佳实践\n\n3. **结果解读指导**：\n   - 输出文件的格式和内容说明\n   - 如何理解异常检测结果和异常分数\n   - 如何生成和解读可视化图表（类似你提到的图片结果）\n\n4. **完整示例演示**：\n   - 提供一个从原始CSV数据到最终结果的完整操作示例\n   - 包含常见问题的解决方案和故障排除指南\n\n请基于项目现有的文档和代码结构，创建一个循序渐进、易于理解的教学材料，确保初学者能够独立完成整个流程并获得有意义的测试结果。&quot;}},{&quot;id&quot;:1,&quot;type&quot;:3,&quot;image_id_node&quot;:{&quot;image_id&quot;:&quot;1f443beecc462c62dbb510b41281a805a9ed28e5093ebf41c5270f212c57e9cd.png&quot;,&quot;format&quot;:1}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/0temp/算法/卡钻代码label&quot;,&quot;folderRoot&quot;:&quot;D:/0temp/算法/卡钻代码label&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-17T13:04:37.477Z&quot;,&quot;response_text&quot;:&quot;## 三维评估\n\n**理解深度**：高 - 需要深入理解项目架构、数据处理流程和教学材料设计\n**变更范围**：系统 - 需要创建完整的教学指南，涉及多个模块和流程\n**风险等级**：低 - 创建文档材料，不涉及代码修改\n\n基于评估结果，采用**协作规划模式**进行处理。\n\n让我首先获取项目的完整信息，然后制定详细的教学指南创建计划。\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;c9d80031-eba0-4e3f-904c-114d509d41d6&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01WsMhDZbbJ9WLBBn2u1ru3e&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;.\&quot;, \&quot;type\&quot;: \&quot;directory\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;[MEMORY BANK: NOT FOUND]\n\nMemory Bank directory does not exist. Use init-memory-bank to initialize.&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_015MsQVHTKawjxZbz7GNu2eJ&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/0temp/算法/卡钻代码label&quot;,&quot;folderRoot&quot;:&quot;D:/0temp/算法/卡钻代码label&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-17T13:04:48.220Z&quot;,&quot;response_text&quot;:&quot;&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;e2f2b1d2-b237-44ce-931c-d3e4ae5e1bd3&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01CoSWSRrWXhKGm6S8tUKS39&quot;,&quot;tool_name&quot;:&quot;codebase-retrieval&quot;,&quot;input_json&quot;:&quot;{\&quot;information_request\&quot;: \&quot;\\u9879\\u76ee\\u7684\\u6574\\u4f53\\u67b6\\u6784\\u548c\\u4e3b\\u8981\\u529f\\u80fd\\u6a21\\u5757\\uff0c\\u5305\\u62ec\\u6570\\u636e\\u5904\\u7406\\u3001\\u6a21\\u578b\\u8bad\\u7ec3\\u3001\\u6d4b\\u8bd5\\u7b49\\u6838\\u5fc3\\u7ec4\\u4ef6\\u7684\\u4ee3\\u7801\\u7ed3\\u6784\\u548c\\u529f\\u80fd\\u8bf4\\u660e\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the files and directories up to 2 levels deep in ., excluding hidden items:\n./Time-Series-Library-main-loss\n./Time-Series-Library-main-loss\\Time-Series-Library-main\n./Time-Series-Library-main-loss\\算法25-05-07.zip\n./preprocess_data.py\n./使用说明.md\n./预处理使用说明.md\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01WsMhDZbbJ9WLBBn2u1ru3e&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/0temp/算法/卡钻代码label&quot;,&quot;folderRoot&quot;:&quot;D:/0temp/算法/卡钻代码label&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-17T13:04:53.340Z&quot;,&quot;response_text&quot;:&quot;&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;21317ebf-40d9-47bd-8ee2-8586e8df44b7&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_019nj9k8dfywD528JLjDFrys&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;\\u4f7f\\u7528\\u8bf4\\u660e.md\&quot;, \&quot;type\&quot;: \&quot;file\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;The following code sections were retrieved:\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/exp/exp_basic.py\n     1\timport os\n     2\timport torch\n     3\tfrom models import Autoformer, Transformer, TimesNet, Nonstationary_Transformer, DLinear, FEDformer, \\\n     4\t    Informer, LightTS, Reformer, ETSformer, Pyraformer, PatchTST, MICN, Crossformer, FiLM, iTransformer, \\\n     5\t    Koopa, TiDE\n     6\t\n     7\t\n     8\tclass Exp_Basic(object):\n     9\t    def __init__(self, args):\n    10\t        self.args = args\n    11\t        self.model_dict = {\n    12\t            'TimesNet': TimesNet,\n    13\t            'Autoformer': Autoformer,\n    14\t            'Transformer': Transformer,\n    15\t            'Nonstationary_Transformer': Nonstationary_Transformer,\n    16\t            'DLinear': DLinear,\n    17\t            'FEDformer': FEDformer,\n    18\t            'Informer': Informer,\n    19\t            'LightTS': LightTS,\n    20\t            'Reformer': Reformer,\n    21\t            'ETSformer': ETSformer,\n    22\t            'PatchTST': PatchTST,\n    23\t            'Pyraformer': Pyraformer,\n    24\t            'MICN': MICN,\n    25\t            'Crossformer': Crossformer,\n    26\t            'FiLM': FiLM,\n    27\t            'iTransformer': iTransformer,\n    28\t            'Koopa': Koopa,\n    29\t            'TiDE': TiDE,\n    30\t        }\n    31\t        self.device = self._acquire_device()\n    32\t        self.model = self._build_model().to(self.device)\n    33\t\n    34\t    def _build_model(self):\n    35\t        raise NotImplementedError\n    36\t        return None\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/data_provider/data_factory.py\n     1\tfrom data_provider.data_loader import Dataset_ETT_hour, Dataset_ETT_minute, Dataset_Custom, Dataset_M4, PSMSegLoader, \\\n     2\t    MSLSegLoader, SMAPSegLoader, SMDSegLoader, SWATSegLoader, UEAloader,Ning209H83Loader\n     3\tfrom data_provider.uea import collate_fn\n     4\tfrom torch.utils.data import DataLoader\n     5\t\n     6\tdata_dict = {\n     7\t    'ETTh1': MSLSegLoader,\n     8\t    'ETTh2': Dataset_ETT_hour,\n     9\t    'ETTm1': Dataset_ETT_minute,\n    10\t    'ETTm2': Dataset_ETT_minute,\n    11\t    'custom': Dataset_Custom,\n    12\t    'm4': Dataset_M4,\n    13\t    'PSM': PSMSegLoader,\n    14\t    'MSL': MSLSegLoader,\n    15\t    'SMAP': SMAPSegLoader,\n    16\t    'SMD': SMDSegLoader,\n    17\t    'SWAT': SWATSegLoader,\n    18\t    'UEA': UEAloader,\n    19\t    'anomaly_detection':Ning209H83Loader\n    20\t}\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/run.py\n     1\timport argparse\n     2\timport os\n     3\timport torch\n     4\tfrom exp.exp_long_term_forecasting import Exp_Long_Term_Forecast\n     5\tfrom exp.exp_imputation import Exp_Imputation\n     6\tfrom exp.exp_short_term_forecasting import Exp_Short_Term_Forecast\n     7\tfrom exp.exp_anomaly_detection import Exp_Anomaly_Detection\n     8\tfrom exp.exp_classification import Exp_Classification\n     9\tfrom utils.print_args import print_args\n    10\timport random\n    11\timport numpy as np\n    12\t\n    13\tif __name__ == '__main__':\n    14\t    fix_seed = 2021\n    15\t    random.seed(fix_seed)\n    16\t    torch.manual_seed(fix_seed)\n    17\t    np.random.seed(fix_seed)\n    18\t\n    19\t    parser = argparse.ArgumentParser(description='TimesNet')\n    20\t\n    21\t    # basic config\n    22\t    parser.add_argument('--task_name', type=str, required=True, default='anomaly_detection',\n    23\t                        help='task name, options:[long_term_forecast, short_term_forecast, imputation, classification, anomaly_detection]')\n    24\t    parser.add_argument('--is_training', type=int, required=True, default=0, help='status')\n    25\t    parser.add_argument('--model_id', type=str, required=True, default='test', help='model id')\n    26\t    parser.add_argument('--model', type=str, required=True, default='Autoformer',\n    27\t                        help='model name, options: [Autoformer, Transformer, TimesNet]')\n...\n   109\t\n   110\t    args = parser.parse_args()\n   111\t    args.use_gpu = True if torch.cuda.is_available() and args.use_gpu else False\n   112\t\n   113\t    if args.use_gpu and args.use_multi_gpu:\n   114\t        args.devices = args.devices.replace(' ', '')\n   115\t        device_ids = args.devices.split(',')\n   116\t        args.device_ids = [int(id_) for id_ in device_ids]\n   117\t        args.gpu = args.device_ids[0]\n   118\t\n   119\t    print('Args in experiment:')\n   120\t   # print_args(args)\n   121\t\n   122\t    if args.task_name == 'long_term_forecast':\n   123\t        Exp = Exp_Long_Term_Forecast\n   124\t    elif args.task_name == 'short_term_forecast':\n   125\t        Exp = Exp_Short_Term_Forecast\n   126\t    elif args.task_name == 'imputation':\n   127\t        Exp = Exp_Imputation\n   128\t    elif args.task_name == 'anomaly_detection':\n   129\t        Exp = Exp_Anomaly_Detection\n   130\t    elif args.task_name == 'classification':\n   131\t        Exp = Exp_Classification\n   132\t    else:\n   133\t        Exp = Exp_Long_Term_Forecast\n...\n   157\t\n   158\t            print('&gt;&gt;&gt;&gt;&gt;&gt;&gt;start training : {}&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;'.format(setting))\n   159\t            exp.train(setting)\n   160\t\n   161\t            print('&gt;&gt;&gt;&gt;&gt;&gt;&gt;testing : {}&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;'.format(setting))\n   162\t            exp.test(setting)\n   163\t            torch.cuda.empty_cache()\n   164\t    else:\n   165\t        ii = 0\n   166\t        setting = '{}_{}_{}_{}_ft{}_sl{}_ll{}_pl{}_dm{}_nh{}_el{}_dl{}_df{}_fc{}_eb{}_dt{}_{}_{}'.format(\n   167\t            args.task_name,\n   168\t            args.model_id,\n   169\t            args.model,\n   170\t            args.data,\n   171\t            args.features,\n   172\t            args.seq_len,\n   173\t            args.label_len,\n   174\t            args.pred_len,\n   175\t            args.d_model,\n   176\t            args.n_heads,\n   177\t            args.e_layers,\n   178\t            args.d_layers,\n   179\t            args.d_ff,\n   180\t            args.factor,\n   181\t            args.embed,\n   182\t            args.distil,\n   183\t            args.des, ii)\n   184\t\n   185\t        exp = Exp(args)  # set experiments\n   186\t        print('&gt;&gt;&gt;&gt;&gt;&gt;&gt;testing : {}&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;'.format(setting))\n   187\t        exp.test(setting, test=1)\n   188\t        torch.cuda.empty_cache()\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/models/FEDformer.py\n     1\timport torch\n     2\timport torch.nn as nn\n     3\timport torch.nn.functional as F\n     4\tfrom layers.Embed import DataEmbedding\n     5\tfrom layers.AutoCorrelation import AutoCorrelationLayer\n     6\tfrom layers.FourierCorrelation import FourierBlock, FourierCrossAttention\n     7\tfrom layers.MultiWaveletCorrelation import MultiWaveletCross, MultiWaveletTransform\n     8\tfrom layers.Autoformer_EncDec import Encoder, Decoder, EncoderLayer, DecoderLayer, my_Layernorm, series_decomp\n     9\t\n    10\t\n    11\tclass Model(nn.Module):\n    12\t    \&quot;\&quot;\&quot;\n    13\t    FEDformer performs the attention mechanism on frequency domain and achieved O(N) complexity\n    14\t    Paper link: https://proceedings.mlr.press/v162/zhou22g.html\n    15\t    \&quot;\&quot;\&quot;\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/exp/exp_short_term_forecasting.py\n...\n    17\t\n    18\t\n    19\tclass Exp_Short_Term_Forecast(Exp_Basic):\n    20\t    def __init__(self, args):\n    21\t        super(Exp_Short_Term_Forecast, self).__init__(args)\n    22\t\n    23\t    def _build_model(self):\n    24\t        if self.args.data == 'm4':\n    25\t            self.args.pred_len = M4Meta.horizons_map[self.args.seasonal_patterns]  # Up to M4 config\n    26\t            self.args.seq_len = 2 * self.args.pred_len  # input_len = 2*pred_len\n    27\t            self.args.label_len = self.args.pred_len\n    28\t            self.args.frequency_map = M4Meta.frequency_map[self.args.seasonal_patterns]\n    29\t        model = self.model_dict[self.args.model].Model(self.args).float()\n    30\t\n    31\t        if self.args.use_multi_gpu and self.args.use_gpu:\n    32\t            model = nn.DataParallel(model, device_ids=self.args.device_ids)\n    33\t        return model\n    34\t\n    35\t    def _get_data(self, flag):\n    36\t        data_set, data_loader = data_provider(self.args, flag)\n    37\t        return data_set, data_loader\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/data_provider/data_loader.py\n     1\timport os\n     2\timport numpy as np\n     3\timport pandas as pd\n     4\timport glob\n     5\timport re\n     6\timport torch\n     7\tfrom torch.utils.data import Dataset, DataLoader\n     8\tfrom sklearn.preprocessing import StandardScaler\n     9\tfrom utils.timefeatures import time_features\n    10\tfrom data_provider.m4 import M4Dataset, M4Meta\n    11\tfrom data_provider.uea import subsample, interpolate_missing, Normalizer\n    12\tfrom sktime.datasets import load_from_tsfile_to_dataframe\n    13\timport warnings\n    14\timport datetime\n    15\t\n    16\twarnings.filterwarnings('ignore')\n    17\t\n    18\t# 添加格式缓存\n    19\t_FORMAT_CACHE = {}\n    20\t_FIRST_ERROR_PRINTED = False  # 控制只打印首次错误\n    21\t_PROCESSED_TIMESTAMPS = {}  # 存储已处理过的时间戳数组\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/README.md\n     1\t# Time Series Library (TSlib)\n     2\tTSlib is an open-source library for deep learning researchers, especially for deep time series analysis.\n     3\t\n     4\tWe provide a neat code base to evaluate advanced deep time series models or develop your model, which covers five mainstream tasks: **long- and short-term forecasting, imputation, anomaly detection, and classification.**\n     5\t\n     6\t:triangular_flag_on_post:**News** (2023.10) We add an implementation to [iTransformer](https://arxiv.org/abs/2310.06625), which is the state-of-the-art model for long-term forecasting. The official code and complete scripts of iTransformer can be found [here](https://github.com/thuml/iTransformer).\n...\n    62\t\n    63\t```\n    64\t# long-term forecast\n    65\tbash ./scripts/long_term_forecast/ETT_script/TimesNet_ETTh1.sh\n    66\t# short-term forecast\n    67\tbash ./scripts/short_term_forecast/TimesNet_M4.sh\n    68\t# imputation\n    69\tbash ./scripts/imputation/ETT_script/TimesNet_ETTh1.sh\n    70\t# anomaly detection\n    71\tbash ./scripts/anomaly_detection/PSM/TimesNet.sh\n    72\t# classification\n    73\tbash ./scripts/classification/TimesNet.sh\n    74\t```\n    75\t\n    76\t4. Develop your own model.\n    77\t\n    78\t- Add the model file to the folder `./models`. You can follow the `./models/Transformer.py`.\n    79\t- Include the newly added model in the `Exp_Basic.model_dict` of  `./exp/exp_basic.py`.\n    80\t- Create the corresponding scripts under the folder `./scripts`.\n    81\t\n    82\t## Citation\n    83\t\n    84\tIf you find this repo useful, please cite our paper.\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/说明.md\n     1\t# 卡钻检测算法对接文档\n     2\t\n     3\t## 一、系统概述\n     4\t\n     5\t本系统是基于深度学习的时间序列分析算法，专门用于钻井过程中的卡钻检测。系统能够接收实时钻井参数数据，分析时间序列特征，并输出卡钻预警结果。\n     6\t\n     7\t## 二、环境要求\n     8\t\n     9\t- Python 3.7 或以上版本\n    10\t- PyTorch 1.8 或以上版本\n    11\t- CUDA 支持（可选，用于GPU加速）\n    12\t- 依赖库：numpy, pandas, sklearn, matplotlib\n    13\t\n    14\t## 三、数据接口规范\n    15\t\n    16\t### 1. 输入数据格式\n    17\t\n    18\t#### 1.1 训练数据\n    19\t- 文件格式：`.npy`或`.csv`\n    20\t- 数据结构：二维数组，每行为一个时间点的多个参数，最后一列为时间戳\n    21\t- 必要字段：钻井参数（扭矩、钻压、钻速等）+ 时间戳\n    22\t\n    23\t#### 1.2 测试/预测数据\n    24\t- 文件格式：`.npy`或`.csv`\n    25\t- 数据结构：与训练数据相同\n    26\t- 时间戳格式：支持多种格式，包括标准时间字符串、时间戳数值等\n    27\t\n    28\t### 2. 输出数据格式\n    29\t\n    30\t- 异常检测结果：二进制数组，每个元素对应一个时间窗口的检测结果（0=正常，1=卡钻）\n    31\t- 异常分数：浮点型数组，数值越大表示异常可能性越高\n    32\t- 汇总报告：包含检测结果统计、异常比例等信息\n    33\t\n    34\t## 四、算法参数配置\n    35\t\n    36\t主要参数包括：\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/exp/exp_long_term_forecasting.py\n...\n    14\t\n    15\t\n    16\tclass Exp_Long_Term_Forecast(Exp_Basic):\n    17\t    def __init__(self, args):\n    18\t        super(Exp_Long_Term_Forecast, self).__init__(args)\n    19\t\n    20\t    def _build_model(self):\n    21\t        model = self.model_dict[self.args.model].Model(self.args).float()\n    22\t\n    23\t        if self.args.use_multi_gpu and self.args.use_gpu:\n    24\t            model = nn.DataParallel(model, device_ids=self.args.device_ids)\n    25\t        return model\n    26\t\n    27\t    def _get_data(self, flag):\n    28\t        data_set, data_loader = data_provider(self.args, flag)\n    29\t        return data_set, data_loader\n    30\t\n    31\t    def _select_optimizer(self):\n    32\t        model_optim = optim.Adam(self.model.parameters(), lr=self.args.learning_rate)\n    33\t        return model_optim\n    34\t\n    35\t    def _select_criterion(self):\n    36\t        criterion = nn.MSELoss()\n    37\t        return criterion\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/exp/exp_anomaly_detection.py\n...\n    28\t\n    29\t\n    30\tclass Exp_Anomaly_Detection(Exp_Basic):\n    31\t    # 添加静态缓存变量用于存储训练集重建误差\n    32\t    _train_energy_cache = None\n    33\t    _model_cache_id = None\n    34\t    \n    35\t    def __init__(self, args):\n    36\t        super(Exp_Anomaly_Detection, self).__init__(args)\n    37\t\n    38\t    def _build_model(self):\n    39\t        model = self.model_dict[self.args.model].Model(self.args).float()\n    40\t\n    41\t        if self.args.use_multi_gpu and self.args.use_gpu:\n    42\t            model = nn.DataParallel(model, device_ids=self.args.device_ids)\n    43\t        return model\n    44\t\n    45\t    def _get_data(self, flag):\n    46\t        data_set, data_loader = data_provider(self.args, flag)\n    47\t        return data_set, data_loader\n    48\t\n    49\t    def _select_optimizer(self):\n    50\t        model_optim = optim.Adam(self.model.parameters(), lr=self.args.learning_rate)\n    51\t        return model_optim\n    52\t\n    53\t    def _select_criterion(self):\n    54\t        criterion = nn.MSELoss()\n    55\t        return criterion\n...\n   113\t\n   114\t            self.model.train()\n   115\t            epoch_time = time.time()\n   116\t            for i, (batch_x, batch_y) in enumerate(train_loader):\n   117\t                iter_count += 1\n   118\t                model_optim.zero_grad()\n   119\t\n   120\t                batch_x = batch_x.float().to(self.device)\n   121\t\n   122\t                outputs = self.model(batch_x, None, None, None)\n   123\t\n   124\t                f_dim = -1 if self.args.features == 'MS' else 0\n   125\t                outputs = outputs[:, :, f_dim:]\n   126\t                loss = criterion(outputs, batch_x)\n   127\t                train_loss.append(loss.item())\n   128\t\n   129\t                if (i + 1) % 100 == 0:\n   130\t                    print(\&quot;\\titers: {0}, epoch: {1} | loss: {2:.7f}\&quot;.format(i + 1, epoch + 1, loss.item()))\n   131\t                    speed = (time.time() - time_now) / iter_count\n   132\t                    left_time = speed * ((self.args.train_epochs - epoch) * train_steps - i)\n   133\t                    print('\\tspeed: {:.4f}s/iter; left time: {:.4f}s'.format(speed, left_time))\n   134\t                    iter_count = 0\n   135\t                    time_now = time.time()\n   136\t\n   137\t                loss.backward()\n   138\t                model_optim.step()\n...\nPath: 预处理使用说明.md\n     1\t# CSV数据预处理工具使用说明\n     2\t\n     3\t这个工具可以帮助您将CSV格式的钻井数据文件转换为适合模型使用的NPY格式，并根据卡钻时间记录自动生成标签文件。\n     4\t\n     5\t## 功能特点\n     6\t\n     7\t1. 自动将CSV文件转换为NPY格式\n     8\t2. 自动识别训练文件和测试文件\n     9\t3. 自动合并多个训练文件\n    10\t4. 根据卡钻时间记录生成标签\n    11\t5. 保留时间戳信息用于结果分析\n    12\t6. **生成符合批量测试格式要求的文件名**\n    13\t\n    14\t## 安装依赖\n    15\t\n    16\t```bash\n    17\tpip install numpy pandas tqdm\n    18\t```\n    19\t\n    20\t## 使用方法\n    21\t\n    22\t### 基本用法\n    23\t\n    24\t```bash\n    25\tpython preprocess_data.py --input_dir ./raw_data --output_dir ./dataset/test/泸203H11-5\n    26\t```\n    27\t\n    28\t### 带卡钻时间记录的用法\n    29\t\n    30\t```bash\n    31\tpython preprocess_data.py --input_dir ./raw_data --output_dir ./dataset/test/泸203H11-5 --stuck_file ./卡钻记录.csv\n    32\t```\n    33\t\n    34\t### 完整参数\n    35\t\n    36\t```bash\n    37\tpython preprocess_data.py --input_dir ./raw_data --output_dir ./dataset/test/泸203H11-5 --stuck_file ./卡钻记录.csv --time_col \&quot;时间\&quot; --start_col \&quot;卡钻开始时间\&quot; --end_col \&quot;卡钻结束时间\&quot; --time_format \&quot;%Y-%m-%d %H:%M:%S\&quot;\n    38\t```\n    39\t\n    40\t## 参数说明\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/exp/exp_classification.py\n...\n    65\t\n    66\t        total_loss = np.average(total_loss)\n    67\t\n    68\t        preds = torch.cat(preds, 0)\n    69\t        trues = torch.cat(trues, 0)\n    70\t        probs = torch.nn.functional.softmax(preds)  # (total_samples, num_classes) est. prob. for each class and sample\n    71\t        predictions = torch.argmax(probs, dim=1).cpu().numpy()  # (total_samples,) int class index for each sample\n    72\t        trues = trues.flatten().cpu().numpy()\n    73\t        accuracy = cal_accuracy(predictions, trues)\n    74\t\n    75\t        self.model.train()\n    76\t        return total_loss, accuracy\n    77\t\n    78\t    def train(self, setting):\n    79\t        train_data, train_loader = self._get_data(flag='TRAIN')\n    80\t        vali_data, vali_loader = self._get_data(flag='TEST')\n    81\t        test_data, test_loader = self._get_data(flag='TEST')\n    82\t\n    83\t        path = os.path.join(self.args.checkpoints, setting)\n    84\t        if not os.path.exists(path):\n    85\t            os.makedirs(path)\n...\n   101\t\n   102\t            for i, (batch_x, label, padding_mask) in enumerate(train_loader):\n   103\t                iter_count += 1\n   104\t                model_optim.zero_grad()\n   105\t\n   106\t                batch_x = batch_x.float().to(self.device)\n   107\t                padding_mask = padding_mask.float().to(self.device)\n   108\t                label = label.to(self.device)\n   109\t\n   110\t                outputs = self.model(batch_x, padding_mask, None, None)\n   111\t                loss = criterion(outputs, label.long().squeeze(-1))\n   112\t                train_loss.append(loss.item())\n   113\t\n   114\t                if (i + 1) % 100 == 0:\n   115\t                    print(\&quot;\\titers: {0}, epoch: {1} | loss: {2:.7f}\&quot;.format(i + 1, epoch + 1, loss.item()))\n   116\t                    speed = (time.time() - time_now) / iter_count\n   117\t                    left_time = speed * ((self.args.train_epochs - epoch) * train_steps - i)\n   118\t                    print('\\tspeed: {:.4f}s/iter; left time: {:.4f}s'.format(speed, left_time))\n   119\t                    iter_count = 0\n   120\t                    time_now = time.time()\n...\nPath: preprocess_data.py\n...\n     4\t\n     5\tdef process_single_csv(file_path, time_col, output_dir, stuck_periods=None, time_format='%Y-%m-%d %H:%M:%S'):\n     6\t    \&quot;\&quot;\&quot;处理单个CSV文件，将其转换为NPY并生成标签\&quot;\&quot;\&quot;\n     7\t    print(f\&quot;处理文件: {file_path}\&quot;)\n     8\t    \n     9\t    # 读取CSV文件\n    10\t    df = pd.read_csv(file_path)\n    11\t    \n    12\t    # 确保时间列存在\n    13\t    if time_col not in df.columns:\n    14\t        print(f\&quot;错误: {file_path} 中没有找到时间列 '{time_col}'\&quot;)\n    15\t        return None, None\n    16\t    \n    17\t    # 转换时间列\n    18\t    try:\n    19\t        df[time_col] = pd.to_datetime(df[time_col], format=time_format)\n    20\t    except Exception as e:\n    21\t        print(f\&quot;时间列转换失败: {e}\&quot;)\n    22\t        print(f\&quot;尝试自动识别时间格式...\&quot;)\n    23\t        df[time_col] = pd.to_datetime(df[time_col])\n    24\t    \n    25\t    # 排序\n    26\t    df = df.sort_values(by=time_col)\n    27\t    \n    28\t    # 删除重复时间戳行\n    29\t    df = df.drop_duplicates(subset=[time_col])\n    30\t    \n    31\t    # 提取数值列（排除时间列和非数值列）\n    32\t    numeric_cols = df.select_dtypes(include=['number']).columns.tolist()\n    33\t    if time_col in numeric_cols:\n    34\t        numeric_cols.remove(time_col)\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/data_processing/可视化_特征/处理.py\n     1\timport pandas as pd\n     2\timport numpy as np\n     3\tfrom sklearn.impute import SimpleImputer\n     4\tfrom scipy import stats\n     5\t\n     6\t# 读取CSV文件\n     7\tdf = pd.read_csv(\&quot;泸203H11-4-02205.csv\&quot;)\n     8\t\n     9\t# 1. 填充缺失值\n    10\t# 使用均值填充数值型特征\n    11\tnumeric_features = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP']\n    12\timputer = SimpleImputer(strategy='mean')\n    13\tdf[numeric_features] = imputer.fit_tran...&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01CoSWSRrWXhKGm6S8tUKS39&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/0temp/算法/卡钻代码label&quot;,&quot;folderRoot&quot;:&quot;D:/0temp/算法/卡钻代码label&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-17T13:05:05.922Z&quot;,&quot;response_text&quot;:&quot;&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;e5a499f9-9bc3-4939-8c57-c4a04333a4f8&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_012REEj947xaSbaCS1VpmZzM&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;\\u9884\\u5904\\u7406\\u4f7f\\u7528\\u8bf4\\u660e.md\&quot;, \&quot;type\&quot;: \&quot;file\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the result of running `cat -n` on 使用说明.md:\n     1\t# 卡钻检测使用说明\n     2\t\n     3\t## 目录结构\n     4\t\n     5\t使用本软件，需要按照以下目录结构准备数据：\n     6\t\n     7\t```\n     8\t./dataset/test/泸203H11-5/\n     9\t    ├── train自201H54-3.npy     # 训练数据文件（固定文件名）\n    10\t    ├── 实时数据1.npy           # 测试文件1\n    11\t    ├── 实时数据1_label.npy     # 测试文件1对应的标签文件（可选）\n    12\t    ├── 实时数据2.npy           # 测试文件2\n    13\t    └── 实时数据2_label.npy     # 测试文件2对应的标签文件（可选）\n    14\t```\n    15\t\n    16\t注意：\n    17\t- 测试文件可以有多个，命名不限\n    18\t- 标签文件必须遵循\&quot;测试文件名_label.npy\&quot;的命名规则\n    19\t- 标签文件为可选项（使用无标签模式时不需要）\n    20\t- 训练数据文件名为固定的\&quot;train自201H54-3.npy\&quot;\n    21\t\n    22\t## 测试方法\n    23\t\n    24\t### 单个文件测试\n    25\t\n    26\t#### 标准模式（使用标签）\n    27\t\n    28\t```bash\n    29\tpython run.py --task_name=anomaly_detection --is_training=0 --root_path=./dataset/test --data_path=泸203H11-5 --model_id=泸203H11-1 --model=FEDformer --data=anomaly_detection --features=M --seq_len=96 --label_len=48 --pred_len=96 --e_layers=3 --d_layers=1 --factor=5 --enc_in=12 --dec_in=12 --c_out=12 --d_model=128 --d_ff=512 --des=Exp --itr=1 --top_k=5 --test_file=实时数据2.npy --label_file=实时数据2_label.npy\n    30\t```\n    31\t\n    32\t#### 无标签模式\n    33\t\n    34\t```bash\n    35\tpython run.py --task_name=anomaly_detection --is_training=0 --root_path=./dataset/test --data_path=泸203H11-5 --model_id=泸203H11-1 --model=FEDformer --data=anomaly_detection --features=M --seq_len=96 --label_len=48 --pred_len=96 --e_layers=3 --d_layers=1 --factor=5 --enc_in=12 --dec_in=12 --c_out=12 --d_model=128 --d_ff=512 --des=Exp --itr=1 --top_k=5 --test_file=实时数据2.npy --no_label\n    36\t```\n    37\t\n    38\t### 批量测试\n    39\t\n    40\t#### 直接使用默认配置（推荐方式）\n    41\t\n    42\t直接编辑`batch_test.py`文件开头的配置部分，设置所需参数：\n    43\t\n    44\t```python\n    45\t# ============= 参数设置区域（直接修改这里的值） =============\n    46\t# 全局控制变量 - 标签模式设置\n    47\tUSE_LABEL = False  # True表示使用标签模式，False表示无标签模式\n    48\t\n    49\t# 默认参数配置（必填参数在此处设置，无需通过命令行指定）\n    50\tDEFAULT_CONFIG = {\n    51\t    \&quot;root_path\&quot;: \&quot;./dataset/test\&quot;,        # 数据根目录\n    52\t    \&quot;data_path\&quot;: \&quot;泸203H11-5\&quot;,            # 数据子目录\n    53\t    \&quot;model_id\&quot;: \&quot;泸203H11-1\&quot;,             # 模型ID\n    54\t    # 其他参数...\n    55\t}\n    56\t```\n    57\t\n    58\t然后直接运行（无需任何命令行参数）：\n    59\t\n    60\t```bash\n    61\tpython batch_test.py\n    62\t```\n    63\t\n    64\t#### 自动扫描测试文件（命令行方式）\n    65\t\n    66\t```bash\n    67\tpython batch_test.py --root_path=./dataset/test --data_path=泸203H11-5\n    68\t```\n    69\t\n    70\t#### 使用命令行切换无标签模式\n    71\t\n    72\t```bash\n    73\tpython batch_test.py --root_path=./dataset/test --data_path=泸203H11-5 --no_label\n    74\t```\n    75\t\n    76\t#### 指定匹配模式\n    77\t\n    78\t```bash\n    79\tpython batch_test.py --root_path=./dataset/test --data_path=泸203H11-5 --pattern=\&quot;实时*.npy\&quot;\n    80\t```\n    81\t\n    82\t## 参数说明\n    83\t\n    84\t| 参数 | 说明 | 默认值 |\n    85\t| --- | --- | --- |\n    86\t| `--root_path` | 数据根目录 | 配置文件中设置 |\n    87\t| `--data_path` | 数据子目录 | 配置文件中设置 |\n    88\t| `--model_id` | 模型ID | 配置文件中设置 |\n    89\t| `--test_file` | 测试文件名 | 无 |\n    90\t| `--label_file` | 标签文件名 | 无 |\n    91\t| `--no_label` | 无标签模式（不使用标签文件） | 由USE_LABEL控制 |\n    92\t| `--test_dir` | 指定测试文件目录 | 配置文件中设置 |\n    93\t| `--pattern` | 文件名匹配模式（如\&quot;*.npy\&quot;） | 配置文件中设置 |\n    94\t| `--exclude` | 排除的文件模式（用逗号分隔） | 配置文件中设置 |\n    95\t\n    96\t## 配置优先级\n    97\t\n    98\t1. 命令行参数（最高优先级）\n    99\t2. 代码中的配置（DEFAULT_CONFIG）\n   100\t3. 程序内部默认值（最低优先级）\n   101\t\n   102\t## 注意事项\n   103\t\n   104\t1. 训练数据文件名是固定的：`train自201H54-3.npy`\n   105\t2. 在无标签模式下，程序不会计算准确率、精确率等指标\n   106\t3. 结果文件名格式为：`anomaly_results_{测试文件名}_{时间戳}.csv`\n   107\t4. 编辑配置后可以直接运行`python batch_test.py`，无需提供任何命令行参数 \nTotal lines in file: 107\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_019nj9k8dfywD528JLjDFrys&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/0temp/算法/卡钻代码label&quot;,&quot;folderRoot&quot;:&quot;D:/0temp/算法/卡钻代码label&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-17T13:05:14.920Z&quot;,&quot;response_text&quot;:&quot;&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;ebd424bd-e4a7-4602-a1fa-7d31594b9b21&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01RyvrbFKMUP1138kGGiZtvc&quot;,&quot;tool_name&quot;:&quot;codebase-retrieval&quot;,&quot;input_json&quot;:&quot;{\&quot;information_request\&quot;: \&quot;\\u6279\\u91cf\\u6d4b\\u8bd5\\u811a\\u672cbatch_test.py\\u7684\\u5b8c\\u6574\\u4ee3\\u7801\\u548c\\u529f\\u80fd\\uff0c\\u5305\\u62ec\\u53c2\\u6570\\u914d\\u7f6e\\u3001\\u6587\\u4ef6\\u626b\\u63cf\\u3001\\u6d4b\\u8bd5\\u6267\\u884c\\u7b49\\u6838\\u5fc3\\u529f\\u80fd\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the result of running `cat -n` on 预处理使用说明.md:\n     1\t# CSV数据预处理工具使用说明\n     2\t\n     3\t这个工具可以帮助您将CSV格式的钻井数据文件转换为适合模型使用的NPY格式，并根据卡钻时间记录自动生成标签文件。\n     4\t\n     5\t## 功能特点\n     6\t\n     7\t1. 自动将CSV文件转换为NPY格式\n     8\t2. 自动识别训练文件和测试文件\n     9\t3. 自动合并多个训练文件\n    10\t4. 根据卡钻时间记录生成标签\n    11\t5. 保留时间戳信息用于结果分析\n    12\t6. **生成符合批量测试格式要求的文件名**\n    13\t\n    14\t## 安装依赖\n    15\t\n    16\t```bash\n    17\tpip install numpy pandas tqdm\n    18\t```\n    19\t\n    20\t## 使用方法\n    21\t\n    22\t### 基本用法\n    23\t\n    24\t```bash\n    25\tpython preprocess_data.py --input_dir ./raw_data --output_dir ./dataset/test/泸203H11-5\n    26\t```\n    27\t\n    28\t### 带卡钻时间记录的用法\n    29\t\n    30\t```bash\n    31\tpython preprocess_data.py --input_dir ./raw_data --output_dir ./dataset/test/泸203H11-5 --stuck_file ./卡钻记录.csv\n    32\t```\n    33\t\n    34\t### 完整参数\n    35\t\n    36\t```bash\n    37\tpython preprocess_data.py --input_dir ./raw_data --output_dir ./dataset/test/泸203H11-5 --stuck_file ./卡钻记录.csv --time_col \&quot;时间\&quot; --start_col \&quot;卡钻开始时间\&quot; --end_col \&quot;卡钻结束时间\&quot; --time_format \&quot;%Y-%m-%d %H:%M:%S\&quot;\n    38\t```\n    39\t\n    40\t## 参数说明\n    41\t\n    42\t| 参数 | 说明 | 默认值 |\n    43\t|------|------|--------|\n    44\t| --input_dir | CSV文件所在目录 | (必填) |\n    45\t| --output_dir | NPY输出目录 | (必填) |\n    46\t| --stuck_file | 卡钻时间记录文件（CSV格式） | (可选) |\n    47\t| --time_col | 时间列名称 | \&quot;时间\&quot; |\n    48\t| --start_col | 卡钻开始时间列名称 | \&quot;卡钻开始时间\&quot; |\n    49\t| --end_col | 卡钻结束时间列名称 | \&quot;卡钻结束时间\&quot; |\n    50\t| --time_format | 时间格式 | \&quot;%Y-%m-%d %H:%M:%S\&quot; |\n    51\t\n    52\t## 卡钻时间记录文件格式\n    53\t\n    54\t卡钻时间记录文件应为CSV格式，至少包含两列：卡钻开始时间和卡钻结束时间。例如：\n    55\t\n    56\t```\n    57\t卡钻开始时间,卡钻结束时间\n    58\t2023-01-01 08:30:00,2023-01-01 09:15:00\n    59\t2023-01-02 14:20:00,2023-01-02 15:45:00\n    60\t```\n    61\t\n    62\t## 处理流程\n    63\t\n    64\t1. 自动识别训练文件和测试文件\n    65\t   - 文件名中包含\&quot;train\&quot;或\&quot;训练\&quot;的被识别为训练文件\n    66\t   - 文件名中包含\&quot;test\&quot;或\&quot;测试\&quot;的被识别为测试文件\n    67\t   - 其他文件会询问用户进行分类\n    68\t\n    69\t2. 处理训练文件\n    70\t   - 合并所有训练文件数据\n    71\t   - 保存为固定文件名\&quot;train自201H54-3.npy\&quot;\n    72\t\n    73\t3. 处理测试文件\n    74\t   - 单独处理每个测试文件\n    75\t   - 根据卡钻时间记录生成标签\n    76\t   - 保存为原文件名.npy和原文件名_label.npy\n    77\t   - **注意：标签文件会自动保存为\&quot;文件名_label.npy\&quot;格式，以配合批量测试功能**\n    78\t\n    79\t4. 数据格式\n    80\t   - NPY文件最后一列保存了时间戳信息\n    81\t   - 标签文件中，1表示卡钻，0表示正常\n    82\t\n    83\t## 批量处理流程\n    84\t\n    85\t1. 使用预处理工具处理原始CSV数据\n    86\t   ```bash\n    87\t   python preprocess_data.py --input_dir ./raw_data --output_dir ./dataset/test/泸203H11-5 --stuck_file ./卡钻记录.csv\n    88\t   ```\n    89\t\n    90\t2. 使用批量测试脚本进行测试\n    91\t   ```bash\n    92\t   python batch_test.py --root_path=./dataset/test --data_path=泸203H11-5\n    93\t   ```\n    94\t\n    95\t## 注意事项\n    96\t\n    97\t1. 确保CSV文件中包含足够的数值列作为特征\n    98\t2. 时间列必须存在且格式正确\n    99\t3. 卡钻时间记录文件中的时间应与数据文件中的时间格式一致\n   100\t4. 输出目录会自动创建，如已存在则会覆盖同名文件\n   101\t5. **标签文件命名格式为\&quot;原文件名_label.npy\&quot;，这是批量测试自动识别所必需的** \nTotal lines in file: 101\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_012REEj947xaSbaCS1VpmZzM&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/0temp/算法/卡钻代码label&quot;,&quot;folderRoot&quot;:&quot;D:/0temp/算法/卡钻代码label&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-17T13:05:21.391Z&quot;,&quot;response_text&quot;:&quot;&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;c9ee79d6-e85f-4b4c-b679-2566e49f33fd&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01G8zeQmke8ruDwYEqt2Aqmj&quot;,&quot;tool_name&quot;:&quot;codebase-retrieval&quot;,&quot;input_json&quot;:&quot;{\&quot;information_request\&quot;: \&quot;\\u9879\\u76ee\\u4e2d\\u7684\\u53ef\\u89c6\\u5316\\u529f\\u80fd\\u548c\\u7ed3\\u679c\\u8f93\\u51fa\\u683c\\u5f0f\\uff0c\\u5305\\u62ec\\u56fe\\u8868\\u751f\\u6210\\u3001CSV\\u7ed3\\u679c\\u6587\\u4ef6\\u683c\\u5f0f\\u3001\\u5f02\\u5e38\\u68c0\\u6d4b\\u7ed3\\u679c\\u7684\\u5c55\\u793a\\u65b9\\u5f0f\\u7b49\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;The following code sections were retrieved:\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/batch_test.py\n     1\timport os\n     2\timport subprocess\n     3\timport argparse\n     4\timport glob\n     5\timport re\n     6\timport numpy as np\n     7\tfrom sklearn.preprocessing import StandardScaler\n     8\timport time\n     9\timport io\n    10\timport sys\n    11\tfrom collections import defaultdict\n    12\t\n    13\t# ============= 参数设置区域（直接修改这里的值） =============\n    14\t# 全局控制变量 - 标签模式设置\n    15\tUSE_LABEL = False  # True表示使用标签模式，False表示无标签模式\n    16\t\n    17\t# 默认参数配置（必填参数在此处设置，无需通过命令行指定）\n    18\tDEFAULT_CONFIG = {\n    19\t    \&quot;root_path\&quot;: \&quot;./dataset/test\&quot;,        # 数据根目录\n    20\t    \&quot;data_path\&quot;: \&quot;泸203H11-5\&quot;,            # 数据子目录\n    21\t    \&quot;model_id\&quot;: \&quot;泸203H11-2\&quot;,             # 模型ID\n    22\t    \&quot;model\&quot;: \&quot;FEDformer\&quot;,                 # 模型名称\n    23\t    \&quot;seq_len\&quot;: 96,                        # 序列长度\n    24\t    \&quot;label_len\&quot;: 48,                      # 标签长度\n    25\t    \&quot;pred_len\&quot;: 96,                       # 预测长度\n    26\t    \&quot;test_dir\&quot;: \&quot;./dataset/505\&quot;,         # 测试文件目录，不指定则使用root_path/data_path\n    27\t    \&quot;pattern\&quot;: None,                      # 文件名匹配模式，如 \&quot;*.npy\&quot;\n    28\t    \&quot;exclude\&quot;: \&quot;*_label.npy,train*.npy\&quot;   # 排除的文件模式，用逗号分隔\n    29\t}\n    30\t# =========================================================\n    31\t\n    32\t# 全局缓存，用于存储已加载和处理的数据\n    33\tCACHE = {\n    34\t    'scaler': None,           # 训练好的StandardScaler\n    35\t    'train_data': None,       # 处理后的训练数据\n    36\t    'train_data_loaded': False # 标记是否已加载训练数据\n    37\t}\n    38\t\n    39\tdef extract_anomaly_ratio(output_text):\n    40\t    \&quot;\&quot;\&quot;从输出文本中提取经验卡钻检测异常比例\&quot;\&quot;\&quot;\n    41\t    # 使用正则表达式查找\&quot;经验卡钻检测异常比例: xx.xx%\&quot;\n    42\t    match = re.search(r'经验卡钻检测异常比例: (\\d+\\.\\d+)%', output_text)\n    43\t    if match:\n    44\t        return float(match.group(1))\n    45\t    return None\n    46\t\n    47\tdef ensure_cache_dir():\n    48\t    \&quot;\&quot;\&quot;确保缓存目录存在\&quot;\&quot;\&quot;\n    49\t    cache_dir = './.cache'\n    50\t    if not os.path.exists(cache_dir):\n    51\t        os.makedirs(cache_dir)\n    52\t        print(f\&quot;创建缓存目录: {cache_dir}\&quot;)\n    53\t    return cache_dir\n    54\t\n    55\tdef preprocess_train_data(root_path):\n    56\t    \&quot;\&quot;\&quot;预处理训练数据，避免重复加载和处理\&quot;\&quot;\&quot;\n    57\t    if CACHE['train_data_loaded']:\n    58\t        print(\&quot;使用缓存的训练数据和标准化器\&quot;)\n    59\t        return CACHE['scaler'], CACHE['train_data']\n    60\t    \n    61\t    # 确保缓存目录存在\n    62\t    cache_dir = ensure_cache_dir()\n    63\t    cache_path = os.path.join(cache_dir, 'train_data_cache.npz')\n    64\t    \n    65\t    # 检查是否存在磁盘缓存\n    66\t    if os.path.exists(cache_path):\n    67\t        print(f\&quot;从磁盘缓存加载训练数据: {cache_path}\&quot;)\n    68\t        try:\n    69\t            cache_data = np.load(cache_path, allow_pickle=True)\n    70\t            CACHE['train_data'] = cache_data['train_data']\n    71\t            CACHE['scaler'] = cache_data['scaler'].item()  # 将scaler从数组转回对象\n    72\t            CACHE['train_data_loaded'] = True\n    73\t            print(\&quot;成功从磁盘缓存加载数据\&quot;)\n    74\t            return CACHE['scaler'], CACHE['train_data']\n    75\t        except Exception as e:\n    76\t            print(f\&quot;读取缓存文件失败: {e}，将重新处理训练数据\&quot;)\n    77\t    \n    78\t    print(\&quot;第一次加载训练数据，进行预处理...\&quot;)\n    79\t    start_time = time.time()\n    80\t    \n    81\t    # 尝试加载训练数据\n    82\t    try:\n    83\t        # 首先尝试从当前根目录加载训练数据\n    84\t        train_data_path = os.path.join(root_path, \&quot;train自201H54-3.npy\&quot;)\n    85\t        if not os.path.exists(train_data_path):\n    86\t            # 如果找不到，则尝试从标准位置加载\n    87\t            default_train_path = os.path.join(\&quot;./dataset/test\&quot;, \&quot;train自201H54-3.npy\&quot;)\n    88\t            if os.path.exists(default_train_path):\n    89\t                train_data_path = default_train_path\n    90\t                print(f\&quot;从默认位置加载训练数据: {default_train_path}\&quot;)\n    91\t            else:\n    92\t                raise FileNotFoundError(f\&quot;找不到训练数据，已尝试路径: {train_data_path}, {default_train_path}\&quot;)\n    93\t            \n    94\t        train_data = np.load(train_data_path, allow_pickle=True)\n    95\t        print(f\&quot;成功加载训练数据: {train_data_path}\&quot;)\n    96\t    except Exception as e:\n    97\t        print(f\&quot;加载训练数据失败: {e}\&quot;)\n    98\t        # 创建一个假的训练数据，避免程序崩溃\n    99\t        train_data = np.zeros((100, 13))\n   100\t        print(\&quot;使用空训练数据代替\&quot;)\n   101\t    \n   102\t    # 预处理训练数据\n   103\t    train_data = train_data[:, :-1]  # 去除最后一列\n   104\t    scaler = StandardScaler()\n   105\t    scaler.fit(train_data)\n   106\t    \n   107\t    # 存入缓存\n   108\t    CACHE['scaler'] = scaler\n   109\t    CACHE['train_data'] = train_data\n   110\t    CACHE['train_data_loaded'] = True\n   111\t    \n   112\t    # 将预处理数据保存到磁盘缓存\n   113\t    try:\n   114\t        print(f\&quot;保存训练数据到磁盘缓存: {cache_path}\&quot;)\n   115\t        np.savez(cache_path, train_data=train_data, scaler=np.array([scaler], dtype=object))\n   116\t        print(\&quot;缓存保存成功\&quot;)\n   117\t    except Exception as e:\n   118\t        print(f\&quot;保存缓存失败: {e}\&quot;)\n   119\t    \n   120\t    elapsed = time.time() - start_time\n   121\t    print(f\&quot;训练数据预处理完成，耗时: {elapsed:.2f}秒\&quot;)\n   122\t    \n   123\t    return scaler, train_data\n   124\t\n   125\tdef parse_args():\n   126\t    parser = argparse.ArgumentParser(description='批量异常检测测试')\n   127\t    # 使用默认参数配置，但允许命令行参数覆盖\n   128\t    parser.add_argument('--root_path', type=str, default=DEFAULT_CONFIG[\&quot;root_path\&quot;], help='数据根目录')\n   129\t    parser.add_argument('--model_id', type=str, default=DEFAULT_CONFIG[\&quot;model_id\&quot;], help='模型ID')\n   130\t    parser.add_argument('--model', type=str, default=DEFAULT_CONFIG[\&quot;model\&quot;], help='模型名称')\n   131\t    parser.add_argument('--seq_len', type=int, default=DEFAULT_CONFIG[\&quot;seq_len\&quot;], help='序列长度')\n   132\t    parser.add_argument('--label_len', type=int, default=DEFAULT_CONFIG[\&quot;label_len\&quot;], help='标签长度')\n   133\t    parser.add_argument('--pred_len', type=int, default=DEFAULT_CONFIG[\&quot;pred_len\&quot;], help='预测长度')\n   134\t    parser.add_argument('--test_dir', type=str, default=DEFAULT_CONFIG[\&quot;test_dir\&quot;], help='测试文件目录，如不指定则使用root_path/data_path')\n   135\t    parser.add_argument('--data_path', type=str, default=DEFAULT_CONFIG[\&quot;data_path\&quot;], help='数据子目录')\n   136\t    parser.add_argument('--pattern', type=str, default=DEFAULT_CONFIG[\&quot;pattern\&quot;], help='文件名匹配模式，例如\&quot;*.npy\&quot;')\n   137\t    parser.add_argument('--exclude', type=str, default=DEFAULT_CONFIG[\&quot;exclude\&quot;], help='排除的文件模式，用逗号分隔')\n   138\t    parser.add_argument('--no_label', action='store_true', help='不使用标签文件')\n   139\t    parser.add_argument('--save_summary', action='store_true', help='保存汇总结果到CSV文件')\n   140\t    \n   141\t    # 如果没有提供命令行参数，使用默认配置\n   142\t    if len(os.sys.argv) == 1:\n   143\t        print(\&quot;使用默认配置运行...\&quot;)\n   144\t        print(f\&quot;数据目录: {DEFAULT_CONFIG['test_dir'] if DEFAULT_CONFIG['test_dir'] else DEFAULT_CONFIG['root_path'] + '/' + DEFAULT_CONFIG['data_path']}\&quot;)\n   145\t        print(f\&quot;标签模式: {'' if USE_LABEL else '无'}标签模式\&quot;)\n   146\t        args = parser.parse_args([])\n   147\t    else:\n   148\t        args = parser.parse_args()\n   149\t    return args\n   150\t\n   151\tdef find_test_files(test_dir, pattern=None, exclude_patterns=None, no_label=False):\n   152\t    \&quot;\&quot;\&quot;\n   153\t    查找测试文件及其对应的标签文件\n   154\t    \n   155\t    参数:\n   156\t    - test_dir: 测试文件目录\n   157\t    - pattern: 文件匹配模式（如 \&quot;*.npy\&quot;）\n   158\t    - exclude_patterns: 要排除的文件模式列表\n   159\t    - no_label: 是否不使用标签文件\n   160\t    \n   161\t    返回:\n   162\t    - 包含测试文件和标签文件的字典列表\n   163\t    \&quot;\&quot;\&quot;\n   164\t    if pattern is None:\n   165\t        pattern = \&quot;*.npy\&quot;  # 默认查找所有.npy文件\n   166\t    \n   167\t    # 转换排除模式为列表\n   168\t    if exclude_patterns is None:\n   169\t        exclude_patterns = [\&quot;*_label.npy\&quot;, \&quot;train*.npy\&quot;]  # 默认排除标签文件和训练文件\n   170\t    elif isinstance(exclude_patterns, str):\n   171\t        exclude_patterns = [p.strip() for p in exclude_patterns.split(',')]\n   172\t    \n   173\t    # 确保测试目录存在\n   174\t    if not os.path.exists(test_dir):\n   175\t        print(f\&quot;错误: 测试目录 '{test_dir}' 不存在!\&quot;)\n   176\t        return []\n   177\t        \n   178\t    # 查找所有符合模式的文件\n   179\t    all_files = glob.glob(os.path.join(test_dir, pattern))\n   180\t    \n   181\t    # 排除匹配排除模式的文件\n   182\t    for exclude_pattern in exclude_patterns:\n   183\t        exclude_files = glob.glob(os.path.join(test_dir, exclude_pattern))\n   184\t        all_files = [f for f in all_files if f not in exclude_files]\n   185\t    \n   186\t    test_datasets = []\n   187\t    \n   188\t    for test_file in all_files:\n   189\t        file_name = os.path.basename(test_file)\n   190\t        file_base, file_ext = os.path.splitext(file_name)\n   191\t        \n   192\t        if no_label:\n   193\t            # 不使用标签文件，直接添加测试文件\n   194\t            test_datasets.append({\n   195\t                \&quot;name\&quot;: file_base,\n   196\t                \&quot;test_file\&quot;: file_name,\n   197\t                \&quot;label_file\&quot;: \&quot;dummy_label.npy\&quot;  # 使用虚拟标签文件名\n   198\t            })\n   199\t        else:\n   200\t            # 构建预期的标签文件名\n   201\t            label_file = os.path.join(test_dir, f\&quot;{file_base}_label{file_ext}\&quot;)\n   202\t            \n   203\t            # 检查标签文件是否存在\n   204\t            if os.path.exists(label_file):\n   205\t                test_datasets.append({\n   206\t                    \&quot;name\&quot;: file_base,\n   207\t                    \&quot;test_file\&quot;: file_name,\n   208\t                    \&quot;label_file\&quot;: os.path.basename(label_file)\n   209\t                })\n   210\t            else:\n   211\t                print(f\&quot;警告: 未找到文件 '{test_file}' 对应的标签文件 '{label_file}'\&quot;)\n   212\t    \n   213\t    return test_datasets\n   214\t\n   215\tdef save_anomaly_summary(anomaly_ratios, filename=\&quot;卡钻异常比例汇总.csv\&quot;):\n   216\t    \&quot;\&quot;\&quot;将异常比例汇总保存到CSV文件\&quot;\&quot;\&quot;\n   217\t    import csv\n   218\t    \n   219\t    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:\n   220\t        writer = csv.writer(csvfile)\n   221\t        writer.writerow(['文件名', '经验卡钻检测异常比例(%)', '是否使用经验卡钻检测'])\n   222\t        \n   223\t        for file_name, data in anomaly_ratios.items():\n   224\t            ratio = data.get('ratio', 'N/A')\n   225\t            is_valid = data.get('valid', False)\n   226\t            if ratio != 'N/A':\n   227\t                writer.writerow([file_name, f\&quot;{ratio:.2f}\&quot;, \&quot;是\&quot; if is_valid else \&quot;否\&quot;])\n   228\t            else:\n   229\t                writer.writerow([file_name, \&quot;N/A\&quot;, \&quot;否\&quot;])\n   230\t    \n   231\t    print(f\&quot;异常比例汇总已保存到 {filename}\&quot;)\n   232\t\n   233\tdef main():\n   234\t    args = parse_args()\n   235\t    \n   236\t    # 确保缓存目录存在\n   237\t    ensure_cache_dir()\n   238\t    \n   239\t    # 使用全局控制变量覆盖命令行参数\n   240\t    args.no_label = not USE_LABEL\n   241\t    \n   242\t    # 确定测试目录\n   243\t    test_dir = args.test_dir if args.test_dir else os.path.join(args.root_path, args.data_path)\n   244\t    \n   245\t    # 预处理训练数据，只加载一次\n   246\t    print(\&quot;预处理训练数据...\&quot;)\n   247\t    scaler, train_data = preprocess_train_data(test_dir)\n   248\t    \n   249\t    # 查找测试文件\n   250\t    test_datasets = find_test_files(test_dir, args.pattern, args.exclude, args.no_label)\n   251\t    \n   252\t    if not test_datasets:\n   253\t        print(f\&quot;未在 {test_dir} 中找到有效的测试文件。\&quot;)\n   254\t        return\n   255\t    \n   256\t    print(f\&quot;找到 {len(test_datasets)} 个测试文件:\&quot;)\n   257\t    for i, dataset in enumerate(test_datasets):\n   258\t        if args.no_label:\n   259\t            print(f\&quot;{i+1}. {dataset['name']}: {dataset['test_file']} (无标签模式)\&quot;)\n   260\t        else:\n   261\t            print(f\&quot;{i+1}. {dataset['name']}: {dataset['test_file']} -&gt; {dataset['label_file']}\&quot;)\n   262\t    \n   263\t    # 存储每个文件的经验卡钻检测异常比例\n   264\t    anomaly_ratios = {}\n   265\t    \n   266\t    # 批量执行测试\n   267\t    total_start_time = time.time()\n   268\t    \n   269\t    for idx, dataset in enumerate(test_datasets):\n   270\t        print(f\&quot;\\n开始测试 ({idx+1}/{len(test_datasets)}): {dataset['name']}\&quot;)\n   271\t        test_start_time = time.time()\n   272\t        \n   273\t        # 构建测试文件的完整路径 - 使用测试目录中的文件\n   274\t        test_file_path = os.path.join(test_dir, dataset['test_file'])\n   275\t        \n   276\t        cmd = [\n   277\t            \&quot;python\&quot;, \&quot;run.py\&quot;,\n   278\t            \&quot;--task_name=anomaly_detection\&quot;,\n   279\t            \&quot;--is_training=0\&quot;,\n   280\t            f\&quot;--root_path={test_dir}\&quot;,  # 使用test_dir作为根目录\n   281\t            f\&quot;--data_path={args.data_path}\&quot;,\n   282\t            f\&quot;--model_id={args.model_id}\&quot;,\n   283\t            f\&quot;--model={args.model}\&quot;,\n   284\t            \&quot;--data=anomaly_detection\&quot;,\n   285\t            \&quot;--features=M\&quot;,\n   286\t            f\&quot;--seq_len={args.seq_len}\&quot;,\n   287\t            f\&quot;--label_len={args.label_len}\&quot;,\n   288\t            f\&quot;--pred_len={args.pred_len}\&quot;,\n   289\t            \&quot;--e_layers=3\&quot;,\n   290\t            \&quot;--d_layers=1\&quot;,\n   291\t            \&quot;--factor=5\&quot;,\n   292\t            \&quot;--enc_in=12\&quot;,\n   293\t            \&quot;--dec_in=12\&quot;,\n   294\t            \&quot;--c_out=12\&quot;,\n   295\t            \&quot;--d_model=128\&quot;,\n   296\t            \&quot;--d_ff=512\&quot;,\n   297\t            \&quot;--des='Exp'\&quot;,\n   298\t            \&quot;--itr=1\&quot;,\n   299\t            \&quot;--top_k=5\&quot;,\n   300\t            f\&quot;--test_file={dataset['test_file']}\&quot;,  # 只传文件名，不包含路径\n   301\t        ]\n   302\t        \n   303\t        # 根据全局控制变量决定是否使用标签\n   304\t        if not args.no_label:\n   305\t            cmd.append(f\&quot;--label_file={dataset['label_file']}\&quot;)\n   306\t        else:\n   307\t            cmd.append(\&quot;--no_label\&quot;)\n   308\t        \n   309\t        # 使用环境变量传递缓存信息，告诉数据加载器使用已处理的数据\n   310\t        env = os.environ.copy()\n   311\t        env['USE_CACHED_TRAIN_DATA'] = 'true'\n   312\t        env['CACHE_DIR'] = './.cache'\n   313\t        \n   314\t        # 正确捕获子进程输出 - 使用 Popen 和管道而不是 OutputCapture 类\n   315\t        try:\n   316\t#            print(f\&quot;执行命令: {' '.join(cmd)}\&quot;)\n   317\t            process = subprocess.Popen(\n   318\t                cmd, \n   319\t                env=env, \n   320\t                stdout=subprocess.PIPE,\n   321\t                stderr=subprocess.STDOUT,\n   322\t                universal_newlines=True,\n   323\t                bufsize=1\n   324\t            )\n   325\t            \n   326\t            # 收集所有输出\n   327\t            output_lines = []\n   328\t            for line in process.stdout:\n   329\t                print(line, end='')  # 实时显示输出\n   330\t                output_lines.append(line)\n   331\t            \n   332\t            # 等待进程完成\n   333\t            process.wait()\n   334\t            \n   335\t            # 全部输出文本\n   336\t            output_text = ''.join(output_lines)\n   337\t            \n   338\t            # 提取异常比例\n   339\t            anomaly_ratio = extract_anomaly_ratio(output_text)\n   340\t            \n   341\t            # 判断是否使用了经验卡钻检测方法\n   342\t            is_valid_ratio = False\n   343\t            if anomaly_ratio is not None:\n   344\t                if \&quot;使用经验卡钻检测结果\&quot; in output_text:\n   345\t                    is_valid_ratio = True\n   346\t                \n   347\t                # 将结果存入字典\n   348\t                anomaly_ratios[dataset['name']] = {\n   349\t                    'ratio': anomaly_ratio, \n   350\t                    'valid': is_valid_ratio\n   351\t                }\n   352\t                \n   353\t                print(f\&quot;提取到经验卡钻检测异常比例: {anomaly_ratio:.2f}%，{'使用' if is_valid_ratio else '未使用'}经验卡钻检测结果\&quot;)\n   354\t            else:\n   355\t                print(\&quot;未能提取到经验卡钻检测异常比例\&quot;)\n   356\t                anomaly_ratios[dataset['name']] = {'ratio': 'N/A', 'valid': False}\n   357\t                \n   358\t        except Exception as e:\n   359\t            print(f\&quot;执行测试命令失败: {e}\&quot;)\n   360\t            anomaly_ratios[dataset['name']] = {'ratio': 'N/A', 'valid': False}\n   361\t        \n   362\t        test_elapsed = time.time() - test_start_time\n   363\t        print(f\&quot;完成测试: {dataset['name']}, 耗时: {test_elapsed:.2f}秒\&quot;)\n   364\t    \n   365\t    total_elapsed = time.time() - total_start_time\n   366\t    print(f\&quot;\\n所有测试完成，总耗时: {total_elapsed:.2f}秒，平均每个测试: {total_elapsed/len(test_datasets):.2f}秒\&quot;)\n   367\t\n   368\t    # 打印汇总报告\n   369\t    print(\&quot;\\n===== 经验卡钻检测异常比例汇总 =====\&quot;)\n   370\t    print(f\&quot;{'文件名':&lt;30} {'异常比例(%)':&lt;15} {'是否使用'}\&quot;)\n   371\t    print(\&quot;-\&quot; * 60)\n   372\t    \n   373\t    for file_name, data in anomaly_ratios.items():\n   374\t        ratio = data.get('ratio', 'N/A')\n   375\t        is_valid = data.get('valid', False)\n   376\t        if ratio != 'N/A':\n   377\t            print(f\&quot;{file_name:&lt;30} {ratio:&gt;8.2f}%      {'是' if is_valid else '否'}\&quot;)\n   378\t        else:\n   379\t            print(f\&quot;{file_name:&lt;30} {'N/A':&lt;15} 否\&quot;)\n   380\t    \n   381\t    print(\&quot;-\&quot; * 60)\n   382\t    \n   383\t    # 保存汇总到CSV文件\n   384\t    if args.save_summary or True:  # 默认总是保存\n   385\t        save_anomaly_summary(anomaly_ratios)\n   386\t\n   387\tif __name__ == \&quot;__main__\&quot;:\n   388\t    main() ...\nPath: 使用说明.md\n...\n    33\t\n    34\t```bash\n    35\tpython run.py --task_name=anomaly_detection --is_training=0 --root_path=./dataset/test --data_path=泸203H11-5 --model_id=泸203H11-1 --model=FEDformer --data=anomaly_detection --features=M --seq_len=96 --label_len=48 --pred_len=96 --e_layers=3 --d_layers=1 --factor=5 --enc_in=12 --dec_in=12 --c_out=12 --d_model=128 --d_ff=512 --des=Exp --itr=1 --top_k=5 --test_file=实时数据2.npy --no_label\n    36\t```\n    37\t\n    38\t### 批量测试\n    39\t\n    40\t#### 直接使用默认配置（推荐方式）\n    41\t\n    42\t直接编辑`batch_test.py`文件开头的配置部分，设置所需参数：\n    43\t\n    44\t```python\n    45\t# ============= 参数设置区域（直接修改这里的值） =============\n    46\t# 全局控制变量 - 标签模式设置\n    47\tUSE_LABEL = False  # True表示使用标签模式，False表示无标签模式\n    48\t\n    49\t# 默认参数配置（必填参数在此处设置，无需通过命令行指定）\n    50\tDEFAULT_CONFIG = {\n    51\t    \&quot;root_path\&quot;: \&quot;./dataset/test\&quot;,        # 数据根目录\n    52\t    \&quot;data_path\&quot;: \&quot;泸203H11-5\&quot;,            # 数据子目录\n    53\t    \&quot;model_id\&quot;: \&quot;泸203H11-1\&quot;,             # 模型ID\n    54\t    # 其他参数...\n    55\t}\n    56\t```\n    57\t\n    58\t然后直接运行（无需任何命令行参数）：\n    59\t\n    60\t```bash\n    61\tpython batch_test.py\n    62\t```\n    63\t\n    64\t#### 自动扫描测试文件（命令行方式）\n    65\t\n    66\t```bash\n    67\tpython batch_test.py --root_path=./dataset/test --data_path=泸203H11-5\n    68\t```\n    69\t\n    70\t#### 使用命令行切换无标签模式\n    71\t\n    72\t```bash\n    73\tpython batch_test.py --root_path=./dataset/test --data_path=泸203H11-5 --no_label\n    74\t```\n    75\t\n    76\t#### 指定匹配模式\n    77\t\n    78\t```bash\n    79\tpython batch_test.py --root_path=./dataset/test --data_path=泸203H11-5 --pattern=\&quot;实时*.npy\&quot;\n    80\t```\n    81\t\n    82\t## 参数说明\n...\nPath: 预处理使用说明.md\n...\n    78\t\n    79\t4. 数据格式\n    80\t   - NPY文件最后一列保存了时间戳信息\n    81\t   - 标签文件中，1表示卡钻，0表示正常\n    82\t\n    83\t## 批量处理流程\n    84\t\n    85\t1. 使用预处理工具处理原始CSV数据\n    86\t   ```bash\n    87\t   python preprocess_data.py --input_dir ./raw_data --output_dir ./dataset/test/泸203H11-5 --stuck_file ./卡钻记录.csv\n    88\t   ```\n    89\t\n    90\t2. 使用批量测试脚本进行测试\n    91\t   ```bash\n    92\t   python batch_test.py --root_path=./dataset/test --data_path=泸203H11-5\n    93\t   ```\n    94\t\n    95\t## 注意事项\n    96\t\n    97\t1. 确保CSV文件中包含足够的数值列作为特征\n    98\t2. 时间列必须存在且格式正确\n    99\t3. 卡钻时间记录文件中的时间应与数据文件中的时间格式一致\n   100\t4. 输出目录会自动创建，如已存在则会覆盖同名文件\n   101\t5. **标签文件命名格式为\&quot;原文件名_label.npy\&quot;，这是批量测试自动识别所必需的** ...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/exp/exp_anomaly_detection.py\n...\n   483\t        \n   484\t        postprocess_time = time.time() - postprocess_start\n   485\t        print(f\&quot;[性能统计] 后处理和评估耗时: {postprocess_time:.2f}秒\&quot;)\n   486\t\n   487\t        # 7. 结果保存\n   488\t        save_start = time.time()\n   489\t        # (5) 保存结果\n   490\t        batch_size = self.args.seq_len  # 假设 seq_len 是窗口大小\n   491\t        sampled_indices = np.arange(0, len(test_timestamps), batch_size)\n   492\t\n   493\t        # 生成包含时间戳和测试文件名的唯一结果文件名\n   494\t        timestamp_str = time.strftime(\&quot;%Y%m%d-%H%M%S\&quot;)\n   495\t        test_file_name = getattr(self.args, 'test_file', 'default').replace('.npy', '').replace('.', '_')\n   496\t        result_file_name = f'anomaly_results_{test_file_name}_{timestamp_str}.csv'\n   497\t\n   498\t        # 确保pred是整数类型的0和1\n   499\t        pred = pred.astype(int)\n...\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01RyvrbFKMUP1138kGGiZtvc&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/0temp/算法/卡钻代码label&quot;,&quot;folderRoot&quot;:&quot;D:/0temp/算法/卡钻代码label&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-17T13:05:34.158Z&quot;,&quot;response_text&quot;:&quot;&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;40e08daa-43ba-43ca-9ee2-db96620c6af6&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01UCMagqUc79P6Up6wSmLKDe&quot;,&quot;tool_name&quot;:&quot;init-memory-bank_memory-bank&quot;,&quot;input_json&quot;:&quot;{\&quot;rootPath\&quot;: \&quot;D:/0temp/\\u7b97\\u6cd5/\\u5361\\u94bb\\u4ee3\\u7801label\&quot;, \&quot;force\&quot;: false}&quot;,&quot;mcp_server_name&quot;:&quot;memory-bank&quot;,&quot;mcp_tool_name&quot;:&quot;init-memory-bank&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;现在我已经获得了项目的完整信息，让我创建一个详细的教学指南。\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;The following code sections were retrieved:\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/data_processing/可视化/可视化.py\n     1\timport pandas as pd\n     2\timport matplotlib.pyplot as plt\n     3\timport numpy as np\n     4\t\n     5\t# 读取CSV文件\n     6\tdf = pd.read_csv('长宁H23-2实时数据.csv')\n     7\t\n     8\t# 将时间列转换为datetime类型，并设置为索引\n     9\tdf['date'] = pd.to_datetime(df['date'])\n    10\tdf.set_index('date', inplace=True)\n    11\t\n    12\t# 获取特征列名（排除sfkz）\n    13\tfeatures = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP']\n    14\t\n    15\t# 手动指定每个特征的颜色\n    16\tcolors = [\n    17\t    'blue', 'green', 'red', 'purple', 'orange',\n    18\t    'brown', 'pink', 'gray', 'cyan', 'magenta'\n    19\t]\n...\n    41\t\n    42\t# 绘制每个特征的折线图\n    43\tfor i, (feature, color) in enumerate(zip(features, colors)):\n    44\t    ax = axes[i]\n    45\t    ax.plot(df.index, df[feature], label=feature, color=color)  # 使用指定颜色绘制折线图\n    46\t\n    47\t    # 标记特征sfkz为1的值，使用透明的黄色表示\n    48\t    sfkz_mask = df['sfkz'] == 1\n    49\t    ax.fill_between(df.index, df[feature].min(), df[feature].max(), where=sfkz_mask, color='yellow', alpha=0.3)\n    50\t\n    51\t    # 高亮显示连续长度超过60的区域\n    52\t    segments = highlight_long_segments(df.index, sfkz_mask.values, min_length=50)  # 使用sfkz_mask.values来确保是一个numpy数组\n    53\t    for start, end in segments:\n    54\t        # 高亮显示区域\n    55\t        ax.axvspan(df.index[start], df.index[end - 1], color='red', alpha=0.2)  # 调整红色的透明度为0.2\n    56\t\n    57\t        # 格式化时间段\n    58\t        segment_str = f'{df.index[start]} to {df.index[end - 1]}'\n    59\t        if segment_str not in printed_segments:\n    60\t            printed_segments.add(segment_str)\n    61\t            print(f'高风险 from {df.index[start]} to {df.index[end - 1]}')\n    62\t\n    63\t    ax.set_ylabel(feature)\n    64\t    ax.legend()\n    65\t\n    66\t# 设置整体标题和横坐标标签\n    67\tplt.suptitle('Time Series of Features')\n    68\tplt.xlabel('Date')\n    69\t\n    70\t# 调整布局，避免重叠\n    71\tplt.tight_layout(rect=[0, 0, 1, 0.95])\n    72\t\n    73\t# 显示图形\n    74\tplt.show()\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/utils/tools.py\n...\n    79\t\n    80\t\n    81\tdef visual(true, preds=None, name='./pic/test.pdf'):\n    82\t    \&quot;\&quot;\&quot;\n    83\t    Results visualization\n    84\t    \&quot;\&quot;\&quot;\n    85\t    plt.figure()\n    86\t    plt.plot(true, label='GroundTruth', linewidth=2)\n    87\t    if preds is not None:\n    88\t        plt.plot(preds, label='Prediction', linewidth=2)\n    89\t    plt.legend()\n    90\t    plt.savefig(name, bbox_inches='tight')\n    91\t\n    92\t\n    93\tdef adjustment(gt, pred):\n    94\t    anomaly_state = False\n    95\t    for i in range(len(gt)):\n    96\t        if gt[i] == 1 and pred[i] == 1 and not anomaly_state:\n    97\t            anomaly_state = True\n    98\t            for j in range(i, 0, -1):\n    99\t                if gt[j] == 0:\n   100\t                    break\n   101\t                else:\n   102\t                    if pred[j] == 0:\n   103\t                        pred[j] = 1\n   104\t            for j in range(i, len(gt)):\n   105\t                if gt[j] == 0:\n   106\t                    break\n   107\t                else:\n   108\t                    if pred[j] == 0:\n   109\t                        pred[j] = 1\n   110\t        elif gt[i] == 0:\n   111\t            anomaly_state = False\n   112\t        if anomaly_state:\n   113\t            pred[i] = 1\n   114\t    return gt, pred\n   115\t\n   116\t\n   117\tdef cal_accuracy(y_pred, y_true):\n   118\t    return np.mean(y_pred == y_true)...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/data_processing/可视化/可视化2.py\n...\n    41\t\n    42\t# 绘制每个特征的折线图\n    43\tfor i, (feature, color) in enumerate(zip(features, colors)):\n    44\t    ax = axes[i]\n    45\t    ax.plot(df.index, df[feature], label=feature, color=color)  # 使用指定颜色绘制折线图\n    46\t\n    47\t    # 标记特征sfkz为1的值，使用透明的黄色表示\n    48\t    sfkz_mask = df['sfkz'] == 1\n    49\t    ax.fill_between(df.index, df[feature].min(), df[feature].max(), where=sfkz_mask, color='yellow', alpha=0.3)\n    50\t\n    51\t    # 高亮显示连续长度超过60的区域\n    52\t    segments = highlight_long_segments(df.index, sfkz_mask.values, min_length=60)  # 使用sfkz_mask.values来确保是一个numpy数组\n    53\t    for start, end in segments:\n    54\t        # 高亮显示区域\n    55\t        ax.axvspan(df.index[start], df.index[end - 1], color='red', alpha=0.2)  # 调整红色的透明度为0.2\n    56\t\n    57\t        # 格式化时间段\n    58\t        segment_str = f'{df.index[start]} to {df.index[end - 1]}'\n    59\t        if segment_str not in printed_segments:\n    60\t            printed_segments.add(segment_str)\n    61\t            print(f'高风险 from {df.index[start]} to {df.index[end - 1]}')\n    62\t\n    63\t    ax.set_ylabel(feature)\n    64\t    ax.legend()\n    65\t\n    66\t# 设置整体标题和横坐标标签\n    67\tplt.suptitle('Time Series of Features')\n    68\tplt.xlabel('Date')\n    69\t\n    70\t# 调整布局，避免重叠\n    71\tplt.tight_layout(rect=[0, 0, 1, 0.95])\n    72\t\n    73\t# 显示图形\n    74\tplt.show()\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/data_processing/可视化_特征/可视化_特征.py\n     1\t# 读取CSV文件\n     2\tfile_path = '宁216H5-3实时数据.csv'  # 替换为你的文件路径\n     3\tdf = pd.read_csv(file_path)\n     4\t\n     5\t# 将'date'列设置为时间列，并转换为适当的时间格式（如果需要）\n     6\tdf['date'] = pd.to_datetime(df['date'])\n     7\t\n     8\t# 将DEP列转换为浮点数，以防数据类型不正确\n     9\tdf['DEP'] = pd.to_numeric(df['DEP'], errors='coerce')\n    10\t\n    11\t# 特征列\n    12\tfeatures = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP']\n    13\t\n    14\t# 设置子图布局\n    15\tnum_features = len(features)\n    16\tfig, axes = plt.subplots(num_features, 1, figsize=(10, 2 * num_features), sharex=True)\n    17\t\n    18\t# 使用matplotlib的颜色映射\n    19\tcolors = plt.get_cmap('tab10')\n    20\t\n    21\t# 绘制每个特征的时间变化曲线\n    22\tfor i, feature in enumerate(features):\n    23\t    axes[i].plot(df['date'], df[feature], label=feature, color=colors(i % 10))  # 通过索引选择颜色\n    24\t    axes[i].set_ylabel(feature)\n    25\t    axes[i].legend(loc='upper right')\n    26\t    axes[i].grid(True)\n    27\t\n    28\t    # 禁用科学计数法\n    29\t    axes[i].yaxis.set_major_formatter(ScalarFormatter(useOffset=False))\n    30\t    axes[i].ticklabel_format(style='plain', axis='y')  # 禁用科学计数法\n    31\t\n    32\t# 设置共享的X轴标签\n    33\taxes[-1].set_xlabel('Time')\n    34\t\n    35\t# 设置图表标题\n    36\tfig.suptitle('Feature Trends Over Time', y=1.02)\n    37\t\n    38\t# 调整布局\n    39\tplt.tight_layout()\n    40\tplt.show()\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/exp/exp_anomaly_detection.py\n...\n   157\t\n   158\t    def _save_anomaly_ratio_summary(self):\n   159\t        \&quot;\&quot;\&quot;保存所有异常比例汇总到CSV文件\&quot;\&quot;\&quot;\n   160\t        global ANOMALY_RATIO_SUMMARY, SUMMARY_FILE\n   161\t        \n   162\t        # 如果汇总文件已存在，尝试先读取已有数据\n   163\t        existing_data = {}\n   164\t        if os.path.exists(SUMMARY_FILE):\n   165\t            try:\n   166\t                with open(SUMMARY_FILE, 'r', newline='', encoding='utf-8-sig') as f:\n   167\t                    reader = csv.reader(f)\n   168\t                    headers = next(reader)  # 跳过表头\n   169\t                    for row in reader:\n   170\t                        if len(row) &gt;= 3:\n   171\t                            file_name = row[0]\n   172\t                            ratio = row[1]\n   173\t                            is_valid = row[2]\n   174\t                            existing_data[file_name] = {\n   175\t                                'ratio': ratio,\n   176\t                                'valid': True if is_valid == \&quot;是\&quot; else False\n   177\t                            }\n...\n   200\t        \n   201\t        # 写入CSV文件，明确使用UTF-8编码\n   202\t        try:\n   203\t            with open(SUMMARY_FILE, 'w', newline='', encoding='utf-8') as csvfile:\n   204\t                writer = csv.writer(csvfile)\n   205\t                writer.writerow(['文件名', '经验卡钻检测异常比例(%)', '是否使用经验卡钻检测'])\n   206\t                \n   207\t                for file_name, data in merged_data.items():\n   208\t                    ratio = data.get('ratio', 'N/A')\n   209\t                    is_valid = data.get('valid', False)\n   210\t                    if ratio != 'N/A':\n   211\t                        writer.writerow([file_name, f\&quot;{ratio:.2f}\&quot; if isinstance(ratio, float) else ratio, \&quot;是\&quot; if is_valid else \&quot;否\&quot;])\n   212\t                    else:\n   213\t                        writer.writerow([file_name, \&quot;N/A\&quot;, \&quot;否\&quot;])\n   214\t            \n   215\t            print(f\&quot;异常比例汇总已保存到 {SUMMARY_FILE}\&quot;)\n   216\t            \n   217\t            # 同时保存一份JSON格式的数据，便于其他程序读取\n   218\t            with open(SUMMARY_FILE.replace('.csv', '.json'), 'w', encoding='utf-8') as f:\n   219\t                json.dump(merged_data, f, ensure_ascii=False, indent=2)\n   220\t        except Exception as e:\n   221\t            print(f\&quot;保存汇总文件失败: {e}\&quot;)\n   222\t            # 如果保存失败，尝试使用临时文件名\n   223\t            try:\n   224\t                tmp_file = f\&quot;卡钻异常比例汇总_{int(time.time())}.csv\&quot;\n   225\t                with open(tmp_file, 'w', newline='', encoding='utf-8') as csvfile:\n   226\t                    writer = csv.writer(csvfile)\n   227\t                    writer.writerow(['文件名', '经验卡钻检测异常比例(%)', '是否使用经验卡钻检测'])\n   228\t                    \n   229\t                    for file_name, data in merged_data.items():\n   230\t                        ratio = data.get('ratio', 'N/A')\n   231\t                        is_valid = data.get('valid', False)\n   232\t                        if ratio != 'N/A':\n   233\t                            writer.writerow([file_name, f\&quot;{ratio:.2f}\&quot; if isinstance(ratio, float) else ratio, \&quot;是\&quot; if is_valid else \&quot;否\&quot;])\n   234\t                        else:\n   235\t                            writer.writerow([file_name, \&quot;N/A\&quot;, \&quot;否\&quot;])\n...\n   335\t\n   336\t        attens_energy = np.concatenate(attens_energy, axis=0).reshape(-1)\n   337\t        test_energy = np.array(attens_energy)\n   338\t        test_error_time = time.time() - test_error_start\n   339\t        print(f\&quot;[性能统计] 测试集重建误差计算耗时: {test_error_time:.2f}秒\&quot;)\n   340\t\n   341\t        # 4. 阈值计算\n   342\t        threshold_start = time.time()\n   343\t        combined_energy = np.concatenate([train_energy, test_energy], axis=0)\n   344\t        threshold = np.percentile(combined_energy, 100 - self.args.anomaly_ratio)\n   345\t        print(\&quot;重建误差阈值:\&quot;, threshold)\n   346\t        threshold_time = time.time() - threshold_start\n   347\t        print(f\&quot;[性能统计] 阈值计算耗时: {threshold_time:.2f}秒\&quot;)\n   348\t\n   349\t        # 5. 专业卡钻检测或阈值方法\n   350\t        anomaly_detect_start = time.time()\n   351\t        # 首先尝试使用专业卡钻检测方法\n   352\t        # 直接在代码中设置控制开关，不再依赖环境变量或命令行参数\n   353\t        use_stuck_pipe_detector = True  # 设置为False关闭专业卡钻检测功能，True则启用\n   354\t        \n   355\t        print(f\&quot;专业卡钻检测状态: {'启用' if use_stuck_pipe_detector else '关闭'}\&quot;)\n...\n   387\t                    \n   388\t                    if 0.0001 &lt;= anomaly_ratio &lt;= 0.45:  # 合理的异常比例范围\n   389\t                        print(\&quot;使用经验卡钻检测结果\&quot;)\n   390\t                        pred = anomalies\n   391\t                        test_energy = scores  # 更新异常分数\n   392\t                        # 记录异常比例（使用了经验卡钻检测结果）\n   393\t                        ANOMALY_RATIO_SUMMARY[test_file_base] = {\n   394\t                            'ratio': anomaly_ratio * 100,\n   395\t                            'valid': True\n   396\t                        }\n   397\t                    else:\n   398\t                        print(\&quot;经验卡钻检测异常比例不合理，回退到重建误差方法\&quot;)\n   399\t                        pred = (test_energy &gt; threshold).astype(int)\n   400\t                        # 记录异常比例（未使用经验卡钻检测结果）\n   401\t                        ANOMALY_RATIO_SUMMARY[test_file_base] = {\n   402\t                            'ratio': anomaly_ratio * 100,\n   403\t                            'valid': False\n   404\t                        }\n   405\t                else:\n   406\t                    print(\&quot;没有获取到原始测试数据，使用重建误差方法\&quot;)\n   407\t                    pred = (test_energy &gt; threshold).astype(int)\n   408\t                    # 无经验卡钻检测结果\n   409\t                    ANOMALY_RATIO_SUMMARY[test_file_base] = {\n   410\t                        'ratio': 'N/A',\n   411\t                        'valid': False\n   412\t                    }\n   413\t            except Exception as e:\n   414\t                print(f\&quot;经验卡钻检测失败: {e}，回退到重建误差方法\&quot;)\n   415\t                pred = (test_energy &gt; threshold).astype(int)\n   416\t                # 经验卡钻检测失败\n   417\t                ANOMALY_RATIO_SUMMARY[test_file_base] = {\n   418\t                    'ratio': 'N/A',\n   419\t                    'valid': False\n   420\t                }\n   421\t        else:\n   422\t            # 专业卡钻检测已关闭，直接使用重建误差方法\n   423\t            print(\&quot;专业卡钻检测功能已关闭，使用重建误差方法\&quot;)\n   424\t            pred = (test_energy &gt; threshold).astype(int)\n   425\t            # 专业卡钻检测已关闭\n   426\t            ANOMALY_RATIO_SUMMARY[test_file_base] = {\n   427\t                'ratio': 'N/A',\n   428\t                'valid': False\n   429\t            }\n...\n   483\t        \n   484\t        postprocess_time = time.time() - postprocess_start\n   485\t        print(f\&quot;[性能统计] 后处理和评估耗时: {postprocess_time:.2f}秒\&quot;)\n   486\t\n   487\t        # 7. 结果保存\n   488\t        save_start = time.time()\n   489\t        # (5) 保存结果\n   490\t        batch_size = self.args.seq_len  # 假设 seq_len 是窗口大小\n   491\t        sampled_indices = np.arange(0, len(test_timestamps), batch_size)\n   492\t\n   493\t        # 生成包含时间戳和测试文件名的唯一结果文件名\n   494\t        timestamp_str = time.strftime(\&quot;%Y%m%d-%H%M%S\&quot;)\n   495\t        test_file_name = getattr(self.args, 'test_file', 'default').replace('.npy', '').replace('.', '_')\n   496\t        result_file_name = f'anomaly_results_{test_file_name}_{timestamp_str}.csv'\n   497\t\n   498\t        # 确保pred是整数类型的0和1\n   499\t        pred = pred.astype(int)\n   500\t        \n   501\t        # 创建结果数据框\n   502\t        if no_label_mode:\n   503\t            # 无标签模式下不包含真实标签列\n   504\t            df = pd.DataFrame({\n   505\t                'Timestamp': test_timestamps[sampled_indices],\n   506\t                'Anomaly_Score': test_energy[sampled_indices],\n   507\t                'Predicted_Anomaly': pred[sampled_indices].astype(int)  # 确保是整数类型\n   508\t            })\n   509\t        else:\n   510\t            # 常规模式下包含真实标签列\n   511\t            df = pd.DataFrame({\n   512\t                'Timestamp': test_timestamps[sampled_indices],\n   513\t                'Anomaly_Score': test_energy[sampled_indices],\n   514\t                'Predicted_Anomaly': pred[sampled_indices].astype(int),  # 确保是整数类型\n   515\t                'True_Label': gt[sampled_indices].astype(int)            # 同样确保真实标签也是整数\n   516\t            })\n   517\t            \n   518\t        output_file = os.path.join(folder_path, result_file_name)\n   519\t        df.to_csv(output_file, index=False, encoding='utf-8')\n   520\t        print(f\&quot;预测结果保存至 {output_file}，共 {len(df)} 行\&quot;)\n   521\t        save_time = time.time() - save_start\n   522\t        print(f\&quot;[性能统计] 结果保存耗时: {save_time:.2f}秒\&quot;)\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/data_processing/可视化_特征/处理.py\n     1\timport pandas as pd\n     2\timport numpy as np\n     3\tfrom sklearn.impute import SimpleImputer\n     4\tfrom scipy import stats\n     5\t\n     6\t# 读取CSV文件\n     7\tdf = pd.read_csv(\&quot;泸203H11-4-02205.csv\&quot;)\n     8\t\n     9\t# 1. 填充缺失值\n    10\t# 使用均值填充数值型特征\n    11\tnumeric_features = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP']\n    12\timputer = SimpleImputer(strategy='mean')\n    13\tdf[numeric_features] = imputer.fit_transform(df[numeric_features])\n    14\t\n    15\t# 2. 处理异常值\n    16\t# 使用Z分数方法检测异常值，设定阈值为3\n    17\tfor feature in numeric_features:\n    18\t    z_scores = np.abs(stats.zscore(df[feature]))\n    19\t    df[feature] = np.where(z_scores &gt; 3, np.nan, df[feature])  # 将异常值设为NaN\n    20\t    # 再次填充处理过的NaN值\n    21\t    df[feature] = df[feature].fillna(df[feature].mean())\n    22\t\n    23\t# 保存修正后的数据\n    24\tdf.to_csv(\&quot;cleaned_泸203H11-4-02205.csv\&quot;, index=False)\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/data_processing/异常生成/生成异常时间戳.py\n     1\timport os\n     2\timport pandas as pd\n     3\timport chardet\n     4\t\n     5\t# 输入CSV文件路径\n     6\tinput_file_path = os.path.join('原始', '宁216H2-2(1).csv')\n     7\t\n     8\t# 检测文件编码\n     9\twith open(input_file_path, 'rb') as f:\n    10\t    result = chardet.detect(f.read(10000))\n    11\tencoding = result['encoding']\n    12\t\n    13\t# 读取CSV文件\n    14\tdf = pd.read_csv(input_file_path, encoding=encoding)\n    15\t\n    16\t# 打印DataFrame的列名\n    17\tprint(\&quot;DataFrame的列名：\&quot;, df.columns)\n    18\t\n    19\t# 确保`sfkz`和`date`列存在\n    20\tif 'sfkz' in df.columns and 'date' in df.columns:\n    21\t    # 确保 `date` 列为时间格式\n    22\t    df['date'] = pd.to_datetime(df['date'])\n    23\t\n    24\t    # 初始化变量\n    25\t    in_sequence = False\n    26\t    start_index = 0\n    27\t    sequence_length = 0\n    28\t    sequences = []\n    29\t    high_risk_sequences = []  # 用于保存高风险预警的时间段\n    30\t\n    31\t    # 遍历DataFrame的每一行\n    32\t    for i, row in df.iterrows():\n    33\t        if row['sfkz'] == 1:\n    34\t            if not in_sequence:\n    35\t                in_sequence = True\n    36\t                start_index = i\n    37\t            sequence_length += 1\n    38\t        else:\n    39\t            if in_sequence:\n    40\t                in_sequence = False\n    41\t                end_index = i - 1\n    42\t                start_date = df.loc[start_index, 'date']\n    43\t                end_date = df.loc[end_index, 'date']\n    44\t                # 计算持续时间，时间差值以分钟为单位\n    45\t                duration = (end_date - start_date).total_seconds() / 60  # 持续时间，单位为分钟\n    46\t                sequences.append((sequence_length, start_date, end_date, duration))\n    47\t\n    48\t                # 如果持续时间大于1分钟（30个1值），则添加到高风险预警列表中\n    49\t                if duration &gt; 5:  # 这里的条件是持续时间大于1分钟\n    50\t                    high_risk_sequences.append((sequence_length, start_date, end_date, duration))\n    51\t\n    52\t                sequence_length = 0\n    53\t\n    54\t    # 处理最后的序列\n    55\t    if in_sequence:\n    56\t        end_index = len(df) - 1\n    57\t        start_date = df.loc[start_index, 'date']\n    58\t        end_date = df.loc[end_index, 'date']\n    59\t        duration = (end_date - start_date).total_seconds() / 60  # 持续时间，单位为分钟\n    60\t        sequences.append((sequence_length, start_date, end_date, duration))\n    61\t\n    62\t        # 如果持续时间大于1分钟（30个1值），则添加到高风险预警列表中\n    63\t        if duration &gt; 1:\n    64\t            high_risk_sequences.append((sequence_length, start_date, end_date, duration))\n    65\t\n    66\t    # 输出所有序列的结果\n    67\t    for length, start_date, end_date, duration in sequences:\n    68\t        print(f\&quot;连续1值的个数: {length}, 开始时间戳: {start_date}, 结束时间戳: {end_date}, 持续时间: {duration:.2f} 分钟\&quot;)\n    69\t\n    70\t    # 如果存在高风险预警\n    71\t    if high_risk_sequences:\n    72\t        print(\&quot;\\n高风险预警的时间段：\&quot;)\n    73\t        for length, start_date, end_date, duration in high_risk_sequences:\n    74\t            print(f\&quot;高风险预警 - 连续1值的个数: {length}, 开始时间戳: {start_date}, 结束时间戳: {end_date}, 持续时间: {duration:.2f} 分钟\&quot;)\n    75\t    else:\n    76\t        # 如果没有高风险预警，输出最长的连续1值时间段\n    77\t        if sequences:\n    78\t            max_sequence = max(sequences, key=lambda x: x[0])\n    79\t            length, start_date, end_date, duration = max_sequence\n    80\t            print(\&quot;\\n没有高风险预警\&quot;)\n    81\t            # print(f\&quot;最长连续1值 - 连续1值的个数: {length}, 开始时间戳: {start_date}, 结束时间戳: {end_date}, 持续时间: {duration:.2f} 分钟\&quot;)\n    82\t        else:\n    83\t            print(\&quot;\\n没有连续1值的时间段。\&quot;)\n    84\t\n    85\telse:\n    86\t    print(\&quot;CSV文件中缺少`sfkz`或`date`列\&quot;)\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/data_processing/异常生成/生成时间戳.py\n...\n    59\t\n    60\t# 3. 打印出高风险预警时间（连续5分钟预测为1）\n    61\tdef high_risk_warning(df, time_window_minutes=5):\n    62\t    df['timestamp'] = pd.to_datetime(df['Timestamp'])\n    63\t    high_risk_periods = []\n    64\t    start_idx = None\n    65\t\n    66\t    for i in range(len(df)):\n    67\t        # 如果预测值为1，并且还在同一个高风险段内\n    68\t        if df['Predicted_Anomaly'].iloc[i] == 1:\n    69\t            if start_idx is None:  # 找到一个新的高风险段开始\n    70\t                start_idx = i\n    71\t        else:\n    72\t            if start_idx is not None:  # 结束一个高风险段\n    73\t                duration = (df['timestamp'].iloc[i-1] - df['timestamp'].iloc[start_idx]).total_seconds() / 60\n    74\t                if duration &gt;= time_window_minutes:  # 如果持续时间超过5分钟\n    75\t    ...&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01G8zeQmke8ruDwYEqt2Aqmj&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/0temp/算法/卡钻代码label&quot;,&quot;folderRoot&quot;:&quot;D:/0temp/算法/卡钻代码label&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-17T13:05:48.924Z&quot;,&quot;response_text&quot;:&quot;现在我已经获得了项目的完整信息，让我创建一个详细的教学指南。\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;9f2ef4d5-1457-44f3-8db4-a3f2410e397d&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_vrtx_01J2iNwLCy4Cnvi1B95cz3Vn&quot;,&quot;tool_name&quot;:&quot;zhi___&quot;,&quot;input_json&quot;:&quot;{\&quot;message\&quot;: \&quot;\\u6211\\u6b63\\u5728\\u4e3a\\u60a8\\u521b\\u5efa\\u4e00\\u4e2a\\u5b8c\\u6574\\u7684\\u5361\\u94bb\\u68c0\\u6d4b\\u9879\\u76ee\\u6559\\u5b66\\u6307\\u5357\\u3002\\u57fa\\u4e8e\\u6211\\u5bf9\\u9879\\u76ee\\u4ee3\\u7801\\u7684\\u5206\\u6790\\uff0c\\u6211\\u5c06\\u521b\\u5efa\\u4e00\\u4e2a\\u5faa\\u5e8f\\u6e10\\u8fdb\\u7684\\u6559\\u5b66\\u6750\\u6599\\uff0c\\u5305\\u542b\\u4ee5\\u4e0b\\u4e3b\\u8981\\u90e8\\u5206\\uff1a\\n\\n## \\u6559\\u5b66\\u6307\\u5357\\u7ed3\\u6784\\u89c4\\u5212\\n\\n### 1. \\u9879\\u76ee\\u6982\\u8ff0\\u4e0e\\u73af\\u5883\\u51c6\\u5907\\n- \\u5361\\u94bb\\u68c0\\u6d4b\\u7cfb\\u7edf\\u4ecb\\u7ecd\\n- \\u73af\\u5883\\u914d\\u7f6e\\u548c\\u4f9d\\u8d56\\u5b89\\u88c5\\n- \\u9879\\u76ee\\u76ee\\u5f55\\u7ed3\\u6784\\u8bf4\\u660e\\n\\n### 2. \\u6570\\u636e\\u5904\\u7406\\u5b8c\\u6574\\u6d41\\u7a0b\\n- CSV\\u539f\\u59cb\\u6570\\u636e\\u683c\\u5f0f\\u8981\\u6c42\\n- \\u4f7f\\u7528\\u9884\\u5904\\u7406\\u5de5\\u5177\\u8f6c\\u6362\\u4e3aNPY\\u683c\\u5f0f\\n- \\u5361\\u94bb\\u65f6\\u95f4\\u8bb0\\u5f55\\u6587\\u4ef6\\u7684\\u521b\\u5efa\\u548c\\u4f7f\\u7528\\n- \\u6570\\u636e\\u76ee\\u5f55\\u7684\\u6b63\\u786e\\u7ec4\\u7ec7\\u65b9\\u5f0f\\n\\n### 3. \\u6d4b\\u8bd5\\u6267\\u884c\\u8be6\\u7ec6\\u6b65\\u9aa4\\n- \\u5355\\u4e2a\\u6587\\u4ef6\\u6d4b\\u8bd5\\uff08\\u6709\\u6807\\u7b7e/\\u65e0\\u6807\\u7b7e\\u6a21\\u5f0f\\uff09\\n- \\u6279\\u91cf\\u6d4b\\u8bd5\\u914d\\u7f6e\\u548c\\u6267\\u884c\\n- \\u53c2\\u6570\\u914d\\u7f6e\\u8be6\\u89e3\\u548c\\u6700\\u4f73\\u5b9e\\u8df5\\n\\n### 4. \\u7ed3\\u679c\\u89e3\\u8bfb\\u4e0e\\u53ef\\u89c6\\u5316\\n- \\u8f93\\u51fa\\u6587\\u4ef6\\u683c\\u5f0f\\u8bf4\\u660e\\n- \\u5f02\\u5e38\\u68c0\\u6d4b\\u7ed3\\u679c\\u5206\\u6790\\n- \\u53ef\\u89c6\\u5316\\u56fe\\u8868\\u751f\\u6210\\u548c\\u89e3\\u8bfb\\n\\n### 5. \\u5b8c\\u6574\\u793a\\u4f8b\\u6f14\\u793a\\n- \\u4eceCSV\\u5230\\u6700\\u7ec8\\u7ed3\\u679c\\u7684\\u7aef\\u5230\\u7aef\\u64cd\\u4f5c\\n- \\u5e38\\u89c1\\u95ee\\u9898\\u89e3\\u51b3\\u65b9\\u6848\\n- \\u6545\\u969c\\u6392\\u9664\\u6307\\u5357\\n\\n\\u6211\\u8ba1\\u5212\\u521b\\u5efa\\u4e00\\u4e2a\\u4e3b\\u6559\\u5b66\\u6587\\u6863\\uff0c\\u5e76\\u914d\\u5957\\u4e00\\u4e9b\\u5b9e\\u7528\\u7684\\u811a\\u672c\\u548c\\u793a\\u4f8b\\u3002\\u8fd9\\u6837\\u7684\\u5b89\\u6392\\u662f\\u5426\\u7b26\\u5408\\u60a8\\u7684\\u9700\\u6c42\\uff1f\\u60a8\\u5e0c\\u671b\\u6211\\u91cd\\u70b9\\u5173\\u6ce8\\u54ea\\u4e2a\\u65b9\\u9762\\uff0c\\u6216\\u8005\\u6709\\u4ec0\\u4e48\\u7279\\u6b8a\\u8981\\u6c42\\u5417\\uff1f\&quot;, \&quot;predefined_options\&quot;: [\&quot;\\u7ee7\\u7eed\\u6309\\u8ba1\\u5212\\u521b\\u5efa\\u5b8c\\u6574\\u6559\\u5b66\\u6307\\u5357\&quot;, \&quot;\\u91cd\\u70b9\\u5173\\u6ce8\\u6570\\u636e\\u5904\\u7406\\u6d41\\u7a0b\&quot;, \&quot;\\u91cd\\u70b9\\u5173\\u6ce8\\u6d4b\\u8bd5\\u6267\\u884c\\u6b65\\u9aa4\&quot;, \&quot;\\u91cd\\u70b9\\u5173\\u6ce8\\u7ed3\\u679c\\u89e3\\u8bfb\&quot;, \&quot;\\u9700\\u8981\\u4fee\\u6539\\u6559\\u5b66\\u7ed3\\u6784\&quot;]}&quot;,&quot;mcp_server_name&quot;:&quot;__&quot;,&quot;mcp_tool_name&quot;:&quot;zhi&quot;}},{&quot;id&quot;:2,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:4,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;[MEMORY BANK: INITIALIZED]\n\nMemory Bank has been successfully initialized!\n\nCreated files:\n- productContext.md\n- activeContext.md\n- progress.md\n- decisionLog.md\n- systemPatterns.md\n\n\n\n[ATTENTION] Next steps to execute:\n1. Read and update each memory-bank/*.md file\n2. Fill in relevant content following the guidance in each file\n3. Do not use get-memory-bank-info before completing initial edits\n4. After completing edits, you can start using Memory Bank\n\nImportant file descriptions:\n- productContext.md: Define project goals, features, and architecture\n- activeContext.md: Track current work status and focus\n- progress.md: Manage task progress\n- decisionLog.md: Record important decisions\n- systemPatterns.md: Document code patterns and standards\n\nMaintenance Tips:\n- Keep each file under 300 lines for optimal performance\n- Archive old content daily/weekly to memory-bank/archive/\n- Use update-memory-bank tool for detailed maintenance guidance\n- Check file sizes after each work session&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01UCMagqUc79P6Up6wSmLKDe&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/0temp/算法/卡钻代码label&quot;,&quot;folderRoot&quot;:&quot;D:/0temp/算法/卡钻代码label&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-17T13:05:55.837Z&quot;,&quot;response_text&quot;:&quot;&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;efcb355d-7baf-40d4-abb1-d641267a42e9&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;选择的选项: 重点关注测试执行步骤, 重点关注数据处理流程\n\n❌请记住，不要生成总结性Markdown文档\n❌请记住，不要生成测试脚本\n❌请记住，不要编译，用户自己编译\n❌请记住，不要运行，用户自己运行&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_vrtx_01J2iNwLCy4Cnvi1B95cz3Vn&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/0temp/算法/卡钻代码label&quot;,&quot;folderRoot&quot;:&quot;D:/0temp/算法/卡钻代码label&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-17T13:07:02.496Z&quot;,&quot;response_text&quot;:&quot;&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-2c835f3d-4d73-48ad-b5f8-e0401efa8721&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-c2447a28-ea51-4f38-a4af-fce0d29972a4&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-225e3abe-88ae-4b16-94c6-9a1b80f01bf5&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-a9694cc0-144b-441d-b60a-6a268162b006&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-9584d39a-41f6-4cda-abf8-10fa695b95c9&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-67ae6046-5a6a-4564-9b57-85e420e9bc95&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-948465cf-c6e6-4f00-999a-36ebf4d84e01&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-1b002c55-fdc0-4c60-b733-b7d87aa04f32&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-005371d3-d2b1-43b4-a262-16522e00b320&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-********-0d14-4167-ad8d-27da5c3184a4&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{&quot;2bc0d370-a8a0-4546-8719-e9c095ad911c;toolu_vrtx_015MsQVHTKawjxZbz7GNu2eJ&quot;:{&quot;requestId&quot;:&quot;2bc0d370-a8a0-4546-8719-e9c095ad911c&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_015MsQVHTKawjxZbz7GNu2eJ&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;[MEMORY BANK: NOT FOUND]\n\nMemory Bank directory does not exist. Use init-memory-bank to initialize.&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;c9d80031-eba0-4e3f-904c-114d509d41d6;toolu_vrtx_01WsMhDZbbJ9WLBBn2u1ru3e&quot;:{&quot;requestId&quot;:&quot;c9d80031-eba0-4e3f-904c-114d509d41d6&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01WsMhDZbbJ9WLBBn2u1ru3e&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the files and directories up to 2 levels deep in ., excluding hidden items:\n./Time-Series-Library-main-loss\n./Time-Series-Library-main-loss\\Time-Series-Library-main\n./Time-Series-Library-main-loss\\算法25-05-07.zip\n./preprocess_data.py\n./使用说明.md\n./预处理使用说明.md\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;e2f2b1d2-b237-44ce-931c-d3e4ae5e1bd3;toolu_vrtx_01CoSWSRrWXhKGm6S8tUKS39&quot;:{&quot;requestId&quot;:&quot;e2f2b1d2-b237-44ce-931c-d3e4ae5e1bd3&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01CoSWSRrWXhKGm6S8tUKS39&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;The following code sections were retrieved:\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/exp/exp_basic.py\n     1\timport os\n     2\timport torch\n     3\tfrom models import Autoformer, Transformer, TimesNet, Nonstationary_Transformer, DLinear, FEDformer, \\\n     4\t    Informer, LightTS, Reformer, ETSformer, Pyraformer, PatchTST, MICN, Crossformer, FiLM, iTransformer, \\\n     5\t    Koopa, TiDE\n     6\t\n     7\t\n     8\tclass Exp_Basic(object):\n     9\t    def __init__(self, args):\n    10\t        self.args = args\n    11\t        self.model_dict = {\n    12\t            'TimesNet': TimesNet,\n    13\t            'Autoformer': Autoformer,\n    14\t            'Transformer': Transformer,\n    15\t            'Nonstationary_Transformer': Nonstationary_Transformer,\n    16\t            'DLinear': DLinear,\n    17\t            'FEDformer': FEDformer,\n    18\t            'Informer': Informer,\n    19\t            'LightTS': LightTS,\n    20\t            'Reformer': Reformer,\n    21\t            'ETSformer': ETSformer,\n    22\t            'PatchTST': PatchTST,\n    23\t            'Pyraformer': Pyraformer,\n    24\t            'MICN': MICN,\n    25\t            'Crossformer': Crossformer,\n    26\t            'FiLM': FiLM,\n    27\t            'iTransformer': iTransformer,\n    28\t            'Koopa': Koopa,\n    29\t            'TiDE': TiDE,\n    30\t        }\n    31\t        self.device = self._acquire_device()\n    32\t        self.model = self._build_model().to(self.device)\n    33\t\n    34\t    def _build_model(self):\n    35\t        raise NotImplementedError\n    36\t        return None\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/data_provider/data_factory.py\n     1\tfrom data_provider.data_loader import Dataset_ETT_hour, Dataset_ETT_minute, Dataset_Custom, Dataset_M4, PSMSegLoader, \\\n     2\t    MSLSegLoader, SMAPSegLoader, SMDSegLoader, SWATSegLoader, UEAloader,Ning209H83Loader\n     3\tfrom data_provider.uea import collate_fn\n     4\tfrom torch.utils.data import DataLoader\n     5\t\n     6\tdata_dict = {\n     7\t    'ETTh1': MSLSegLoader,\n     8\t    'ETTh2': Dataset_ETT_hour,\n     9\t    'ETTm1': Dataset_ETT_minute,\n    10\t    'ETTm2': Dataset_ETT_minute,\n    11\t    'custom': Dataset_Custom,\n    12\t    'm4': Dataset_M4,\n    13\t    'PSM': PSMSegLoader,\n    14\t    'MSL': MSLSegLoader,\n    15\t    'SMAP': SMAPSegLoader,\n    16\t    'SMD': SMDSegLoader,\n    17\t    'SWAT': SWATSegLoader,\n    18\t    'UEA': UEAloader,\n    19\t    'anomaly_detection':Ning209H83Loader\n    20\t}\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/run.py\n     1\timport argparse\n     2\timport os\n     3\timport torch\n     4\tfrom exp.exp_long_term_forecasting import Exp_Long_Term_Forecast\n     5\tfrom exp.exp_imputation import Exp_Imputation\n     6\tfrom exp.exp_short_term_forecasting import Exp_Short_Term_Forecast\n     7\tfrom exp.exp_anomaly_detection import Exp_Anomaly_Detection\n     8\tfrom exp.exp_classification import Exp_Classification\n     9\tfrom utils.print_args import print_args\n    10\timport random\n    11\timport numpy as np\n    12\t\n    13\tif __name__ == '__main__':\n    14\t    fix_seed = 2021\n    15\t    random.seed(fix_seed)\n    16\t    torch.manual_seed(fix_seed)\n    17\t    np.random.seed(fix_seed)\n    18\t\n    19\t    parser = argparse.ArgumentParser(description='TimesNet')\n    20\t\n    21\t    # basic config\n    22\t    parser.add_argument('--task_name', type=str, required=True, default='anomaly_detection',\n    23\t                        help='task name, options:[long_term_forecast, short_term_forecast, imputation, classification, anomaly_detection]')\n    24\t    parser.add_argument('--is_training', type=int, required=True, default=0, help='status')\n    25\t    parser.add_argument('--model_id', type=str, required=True, default='test', help='model id')\n    26\t    parser.add_argument('--model', type=str, required=True, default='Autoformer',\n    27\t                        help='model name, options: [Autoformer, Transformer, TimesNet]')\n...\n   109\t\n   110\t    args = parser.parse_args()\n   111\t    args.use_gpu = True if torch.cuda.is_available() and args.use_gpu else False\n   112\t\n   113\t    if args.use_gpu and args.use_multi_gpu:\n   114\t        args.devices = args.devices.replace(' ', '')\n   115\t        device_ids = args.devices.split(',')\n   116\t        args.device_ids = [int(id_) for id_ in device_ids]\n   117\t        args.gpu = args.device_ids[0]\n   118\t\n   119\t    print('Args in experiment:')\n   120\t   # print_args(args)\n   121\t\n   122\t    if args.task_name == 'long_term_forecast':\n   123\t        Exp = Exp_Long_Term_Forecast\n   124\t    elif args.task_name == 'short_term_forecast':\n   125\t        Exp = Exp_Short_Term_Forecast\n   126\t    elif args.task_name == 'imputation':\n   127\t        Exp = Exp_Imputation\n   128\t    elif args.task_name == 'anomaly_detection':\n   129\t        Exp = Exp_Anomaly_Detection\n   130\t    elif args.task_name == 'classification':\n   131\t        Exp = Exp_Classification\n   132\t    else:\n   133\t        Exp = Exp_Long_Term_Forecast\n...\n   157\t\n   158\t            print('&gt;&gt;&gt;&gt;&gt;&gt;&gt;start training : {}&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;'.format(setting))\n   159\t            exp.train(setting)\n   160\t\n   161\t            print('&gt;&gt;&gt;&gt;&gt;&gt;&gt;testing : {}&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;'.format(setting))\n   162\t            exp.test(setting)\n   163\t            torch.cuda.empty_cache()\n   164\t    else:\n   165\t        ii = 0\n   166\t        setting = '{}_{}_{}_{}_ft{}_sl{}_ll{}_pl{}_dm{}_nh{}_el{}_dl{}_df{}_fc{}_eb{}_dt{}_{}_{}'.format(\n   167\t            args.task_name,\n   168\t            args.model_id,\n   169\t            args.model,\n   170\t            args.data,\n   171\t            args.features,\n   172\t            args.seq_len,\n   173\t            args.label_len,\n   174\t            args.pred_len,\n   175\t            args.d_model,\n   176\t            args.n_heads,\n   177\t            args.e_layers,\n   178\t            args.d_layers,\n   179\t            args.d_ff,\n   180\t            args.factor,\n   181\t            args.embed,\n   182\t            args.distil,\n   183\t            args.des, ii)\n   184\t\n   185\t        exp = Exp(args)  # set experiments\n   186\t        print('&gt;&gt;&gt;&gt;&gt;&gt;&gt;testing : {}&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;'.format(setting))\n   187\t        exp.test(setting, test=1)\n   188\t        torch.cuda.empty_cache()\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/models/FEDformer.py\n     1\timport torch\n     2\timport torch.nn as nn\n     3\timport torch.nn.functional as F\n     4\tfrom layers.Embed import DataEmbedding\n     5\tfrom layers.AutoCorrelation import AutoCorrelationLayer\n     6\tfrom layers.FourierCorrelation import FourierBlock, FourierCrossAttention\n     7\tfrom layers.MultiWaveletCorrelation import MultiWaveletCross, MultiWaveletTransform\n     8\tfrom layers.Autoformer_EncDec import Encoder, Decoder, EncoderLayer, DecoderLayer, my_Layernorm, series_decomp\n     9\t\n    10\t\n    11\tclass Model(nn.Module):\n    12\t    \&quot;\&quot;\&quot;\n    13\t    FEDformer performs the attention mechanism on frequency domain and achieved O(N) complexity\n    14\t    Paper link: https://proceedings.mlr.press/v162/zhou22g.html\n    15\t    \&quot;\&quot;\&quot;\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/exp/exp_short_term_forecasting.py\n...\n    17\t\n    18\t\n    19\tclass Exp_Short_Term_Forecast(Exp_Basic):\n    20\t    def __init__(self, args):\n    21\t        super(Exp_Short_Term_Forecast, self).__init__(args)\n    22\t\n    23\t    def _build_model(self):\n    24\t        if self.args.data == 'm4':\n    25\t            self.args.pred_len = M4Meta.horizons_map[self.args.seasonal_patterns]  # Up to M4 config\n    26\t            self.args.seq_len = 2 * self.args.pred_len  # input_len = 2*pred_len\n    27\t            self.args.label_len = self.args.pred_len\n    28\t            self.args.frequency_map = M4Meta.frequency_map[self.args.seasonal_patterns]\n    29\t        model = self.model_dict[self.args.model].Model(self.args).float()\n    30\t\n    31\t        if self.args.use_multi_gpu and self.args.use_gpu:\n    32\t            model = nn.DataParallel(model, device_ids=self.args.device_ids)\n    33\t        return model\n    34\t\n    35\t    def _get_data(self, flag):\n    36\t        data_set, data_loader = data_provider(self.args, flag)\n    37\t        return data_set, data_loader\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/data_provider/data_loader.py\n     1\timport os\n     2\timport numpy as np\n     3\timport pandas as pd\n     4\timport glob\n     5\timport re\n     6\timport torch\n     7\tfrom torch.utils.data import Dataset, DataLoader\n     8\tfrom sklearn.preprocessing import StandardScaler\n     9\tfrom utils.timefeatures import time_features\n    10\tfrom data_provider.m4 import M4Dataset, M4Meta\n    11\tfrom data_provider.uea import subsample, interpolate_missing, Normalizer\n    12\tfrom sktime.datasets import load_from_tsfile_to_dataframe\n    13\timport warnings\n    14\timport datetime\n    15\t\n    16\twarnings.filterwarnings('ignore')\n    17\t\n    18\t# 添加格式缓存\n    19\t_FORMAT_CACHE = {}\n    20\t_FIRST_ERROR_PRINTED = False  # 控制只打印首次错误\n    21\t_PROCESSED_TIMESTAMPS = {}  # 存储已处理过的时间戳数组\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/README.md\n     1\t# Time Series Library (TSlib)\n     2\tTSlib is an open-source library for deep learning researchers, especially for deep time series analysis.\n     3\t\n     4\tWe provide a neat code base to evaluate advanced deep time series models or develop your model, which covers five mainstream tasks: **long- and short-term forecasting, imputation, anomaly detection, and classification.**\n     5\t\n     6\t:triangular_flag_on_post:**News** (2023.10) We add an implementation to [iTransformer](https://arxiv.org/abs/2310.06625), which is the state-of-the-art model for long-term forecasting. The official code and complete scripts of iTransformer can be found [here](https://github.com/thuml/iTransformer).\n...\n    62\t\n    63\t```\n    64\t# long-term forecast\n    65\tbash ./scripts/long_term_forecast/ETT_script/TimesNet_ETTh1.sh\n    66\t# short-term forecast\n    67\tbash ./scripts/short_term_forecast/TimesNet_M4.sh\n    68\t# imputation\n    69\tbash ./scripts/imputation/ETT_script/TimesNet_ETTh1.sh\n    70\t# anomaly detection\n    71\tbash ./scripts/anomaly_detection/PSM/TimesNet.sh\n    72\t# classification\n    73\tbash ./scripts/classification/TimesNet.sh\n    74\t```\n    75\t\n    76\t4. Develop your own model.\n    77\t\n    78\t- Add the model file to the folder `./models`. You can follow the `./models/Transformer.py`.\n    79\t- Include the newly added model in the `Exp_Basic.model_dict` of  `./exp/exp_basic.py`.\n    80\t- Create the corresponding scripts under the folder `./scripts`.\n    81\t\n    82\t## Citation\n    83\t\n    84\tIf you find this repo useful, please cite our paper.\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/说明.md\n     1\t# 卡钻检测算法对接文档\n     2\t\n     3\t## 一、系统概述\n     4\t\n     5\t本系统是基于深度学习的时间序列分析算法，专门用于钻井过程中的卡钻检测。系统能够接收实时钻井参数数据，分析时间序列特征，并输出卡钻预警结果。\n     6\t\n     7\t## 二、环境要求\n     8\t\n     9\t- Python 3.7 或以上版本\n    10\t- PyTorch 1.8 或以上版本\n    11\t- CUDA 支持（可选，用于GPU加速）\n    12\t- 依赖库：numpy, pandas, sklearn, matplotlib\n    13\t\n    14\t## 三、数据接口规范\n    15\t\n    16\t### 1. 输入数据格式\n    17\t\n    18\t#### 1.1 训练数据\n    19\t- 文件格式：`.npy`或`.csv`\n    20\t- 数据结构：二维数组，每行为一个时间点的多个参数，最后一列为时间戳\n    21\t- 必要字段：钻井参数（扭矩、钻压、钻速等）+ 时间戳\n    22\t\n    23\t#### 1.2 测试/预测数据\n    24\t- 文件格式：`.npy`或`.csv`\n    25\t- 数据结构：与训练数据相同\n    26\t- 时间戳格式：支持多种格式，包括标准时间字符串、时间戳数值等\n    27\t\n    28\t### 2. 输出数据格式\n    29\t\n    30\t- 异常检测结果：二进制数组，每个元素对应一个时间窗口的检测结果（0=正常，1=卡钻）\n    31\t- 异常分数：浮点型数组，数值越大表示异常可能性越高\n    32\t- 汇总报告：包含检测结果统计、异常比例等信息\n    33\t\n    34\t## 四、算法参数配置\n    35\t\n    36\t主要参数包括：\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/exp/exp_long_term_forecasting.py\n...\n    14\t\n    15\t\n    16\tclass Exp_Long_Term_Forecast(Exp_Basic):\n    17\t    def __init__(self, args):\n    18\t        super(Exp_Long_Term_Forecast, self).__init__(args)\n    19\t\n    20\t    def _build_model(self):\n    21\t        model = self.model_dict[self.args.model].Model(self.args).float()\n    22\t\n    23\t        if self.args.use_multi_gpu and self.args.use_gpu:\n    24\t            model = nn.DataParallel(model, device_ids=self.args.device_ids)\n    25\t        return model\n    26\t\n    27\t    def _get_data(self, flag):\n    28\t        data_set, data_loader = data_provider(self.args, flag)\n    29\t        return data_set, data_loader\n    30\t\n    31\t    def _select_optimizer(self):\n    32\t        model_optim = optim.Adam(self.model.parameters(), lr=self.args.learning_rate)\n    33\t        return model_optim\n    34\t\n    35\t    def _select_criterion(self):\n    36\t        criterion = nn.MSELoss()\n    37\t        return criterion\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/exp/exp_anomaly_detection.py\n...\n    28\t\n    29\t\n    30\tclass Exp_Anomaly_Detection(Exp_Basic):\n    31\t    # 添加静态缓存变量用于存储训练集重建误差\n    32\t    _train_energy_cache = None\n    33\t    _model_cache_id = None\n    34\t    \n    35\t    def __init__(self, args):\n    36\t        super(Exp_Anomaly_Detection, self).__init__(args)\n    37\t\n    38\t    def _build_model(self):\n    39\t        model = self.model_dict[self.args.model].Model(self.args).float()\n    40\t\n    41\t        if self.args.use_multi_gpu and self.args.use_gpu:\n    42\t            model = nn.DataParallel(model, device_ids=self.args.device_ids)\n    43\t        return model\n    44\t\n    45\t    def _get_data(self, flag):\n    46\t        data_set, data_loader = data_provider(self.args, flag)\n    47\t        return data_set, data_loader\n    48\t\n    49\t    def _select_optimizer(self):\n    50\t        model_optim = optim.Adam(self.model.parameters(), lr=self.args.learning_rate)\n    51\t        return model_optim\n    52\t\n    53\t    def _select_criterion(self):\n    54\t        criterion = nn.MSELoss()\n    55\t        return criterion\n...\n   113\t\n   114\t            self.model.train()\n   115\t            epoch_time = time.time()\n   116\t            for i, (batch_x, batch_y) in enumerate(train_loader):\n   117\t                iter_count += 1\n   118\t                model_optim.zero_grad()\n   119\t\n   120\t                batch_x = batch_x.float().to(self.device)\n   121\t\n   122\t                outputs = self.model(batch_x, None, None, None)\n   123\t\n   124\t                f_dim = -1 if self.args.features == 'MS' else 0\n   125\t                outputs = outputs[:, :, f_dim:]\n   126\t                loss = criterion(outputs, batch_x)\n   127\t                train_loss.append(loss.item())\n   128\t\n   129\t                if (i + 1) % 100 == 0:\n   130\t                    print(\&quot;\\titers: {0}, epoch: {1} | loss: {2:.7f}\&quot;.format(i + 1, epoch + 1, loss.item()))\n   131\t                    speed = (time.time() - time_now) / iter_count\n   132\t                    left_time = speed * ((self.args.train_epochs - epoch) * train_steps - i)\n   133\t                    print('\\tspeed: {:.4f}s/iter; left time: {:.4f}s'.format(speed, left_time))\n   134\t                    iter_count = 0\n   135\t                    time_now = time.time()\n   136\t\n   137\t                loss.backward()\n   138\t                model_optim.step()\n...\nPath: 预处理使用说明.md\n     1\t# CSV数据预处理工具使用说明\n     2\t\n     3\t这个工具可以帮助您将CSV格式的钻井数据文件转换为适合模型使用的NPY格式，并根据卡钻时间记录自动生成标签文件。\n     4\t\n     5\t## 功能特点\n     6\t\n     7\t1. 自动将CSV文件转换为NPY格式\n     8\t2. 自动识别训练文件和测试文件\n     9\t3. 自动合并多个训练文件\n    10\t4. 根据卡钻时间记录生成标签\n    11\t5. 保留时间戳信息用于结果分析\n    12\t6. **生成符合批量测试格式要求的文件名**\n    13\t\n    14\t## 安装依赖\n    15\t\n    16\t```bash\n    17\tpip install numpy pandas tqdm\n    18\t```\n    19\t\n    20\t## 使用方法\n    21\t\n    22\t### 基本用法\n    23\t\n    24\t```bash\n    25\tpython preprocess_data.py --input_dir ./raw_data --output_dir ./dataset/test/泸203H11-5\n    26\t```\n    27\t\n    28\t### 带卡钻时间记录的用法\n    29\t\n    30\t```bash\n    31\tpython preprocess_data.py --input_dir ./raw_data --output_dir ./dataset/test/泸203H11-5 --stuck_file ./卡钻记录.csv\n    32\t```\n    33\t\n    34\t### 完整参数\n    35\t\n    36\t```bash\n    37\tpython preprocess_data.py --input_dir ./raw_data --output_dir ./dataset/test/泸203H11-5 --stuck_file ./卡钻记录.csv --time_col \&quot;时间\&quot; --start_col \&quot;卡钻开始时间\&quot; --end_col \&quot;卡钻结束时间\&quot; --time_format \&quot;%Y-%m-%d %H:%M:%S\&quot;\n    38\t```\n    39\t\n    40\t## 参数说明\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/exp/exp_classification.py\n...\n    65\t\n    66\t        total_loss = np.average(total_loss)\n    67\t\n    68\t        preds = torch.cat(preds, 0)\n    69\t        trues = torch.cat(trues, 0)\n    70\t        probs = torch.nn.functional.softmax(preds)  # (total_samples, num_classes) est. prob. for each class and sample\n    71\t        predictions = torch.argmax(probs, dim=1).cpu().numpy()  # (total_samples,) int class index for each sample\n    72\t        trues = trues.flatten().cpu().numpy()\n    73\t        accuracy = cal_accuracy(predictions, trues)\n    74\t\n    75\t        self.model.train()\n    76\t        return total_loss, accuracy\n    77\t\n    78\t    def train(self, setting):\n    79\t        train_data, train_loader = self._get_data(flag='TRAIN')\n    80\t        vali_data, vali_loader = self._get_data(flag='TEST')\n    81\t        test_data, test_loader = self._get_data(flag='TEST')\n    82\t\n    83\t        path = os.path.join(self.args.checkpoints, setting)\n    84\t        if not os.path.exists(path):\n    85\t            os.makedirs(path)\n...\n   101\t\n   102\t            for i, (batch_x, label, padding_mask) in enumerate(train_loader):\n   103\t                iter_count += 1\n   104\t                model_optim.zero_grad()\n   105\t\n   106\t                batch_x = batch_x.float().to(self.device)\n   107\t                padding_mask = padding_mask.float().to(self.device)\n   108\t                label = label.to(self.device)\n   109\t\n   110\t                outputs = self.model(batch_x, padding_mask, None, None)\n   111\t                loss = criterion(outputs, label.long().squeeze(-1))\n   112\t                train_loss.append(loss.item())\n   113\t\n   114\t                if (i + 1) % 100 == 0:\n   115\t                    print(\&quot;\\titers: {0}, epoch: {1} | loss: {2:.7f}\&quot;.format(i + 1, epoch + 1, loss.item()))\n   116\t                    speed = (time.time() - time_now) / iter_count\n   117\t                    left_time = speed * ((self.args.train_epochs - epoch) * train_steps - i)\n   118\t                    print('\\tspeed: {:.4f}s/iter; left time: {:.4f}s'.format(speed, left_time))\n   119\t                    iter_count = 0\n   120\t                    time_now = time.time()\n...\nPath: preprocess_data.py\n...\n     4\t\n     5\tdef process_single_csv(file_path, time_col, output_dir, stuck_periods=None, time_format='%Y-%m-%d %H:%M:%S'):\n     6\t    \&quot;\&quot;\&quot;处理单个CSV文件，将其转换为NPY并生成标签\&quot;\&quot;\&quot;\n     7\t    print(f\&quot;处理文件: {file_path}\&quot;)\n     8\t    \n     9\t    # 读取CSV文件\n    10\t    df = pd.read_csv(file_path)\n    11\t    \n    12\t    # 确保时间列存在\n    13\t    if time_col not in df.columns:\n    14\t        print(f\&quot;错误: {file_path} 中没有找到时间列 '{time_col}'\&quot;)\n    15\t        return None, None\n    16\t    \n    17\t    # 转换时间列\n    18\t    try:\n    19\t        df[time_col] = pd.to_datetime(df[time_col], format=time_format)\n    20\t    except Exception as e:\n    21\t        print(f\&quot;时间列转换失败: {e}\&quot;)\n    22\t        print(f\&quot;尝试自动识别时间格式...\&quot;)\n    23\t        df[time_col] = pd.to_datetime(df[time_col])\n    24\t    \n    25\t    # 排序\n    26\t    df = df.sort_values(by=time_col)\n    27\t    \n    28\t    # 删除重复时间戳行\n    29\t    df = df.drop_duplicates(subset=[time_col])\n    30\t    \n    31\t    # 提取数值列（排除时间列和非数值列）\n    32\t    numeric_cols = df.select_dtypes(include=['number']).columns.tolist()\n    33\t    if time_col in numeric_cols:\n    34\t        numeric_cols.remove(time_col)\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/data_processing/可视化_特征/处理.py\n     1\timport pandas as pd\n     2\timport numpy as np\n     3\tfrom sklearn.impute import SimpleImputer\n     4\tfrom scipy import stats\n     5\t\n     6\t# 读取CSV文件\n     7\tdf = pd.read_csv(\&quot;泸203H11-4-02205.csv\&quot;)\n     8\t\n     9\t# 1. 填充缺失值\n    10\t# 使用均值填充数值型特征\n    11\tnumeric_features = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP']\n    12\timputer = SimpleImputer(strategy='mean')\n    13\tdf[numeric_features] = imputer.fit_tran...&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;21317ebf-40d9-47bd-8ee2-8586e8df44b7;toolu_vrtx_019nj9k8dfywD528JLjDFrys&quot;:{&quot;requestId&quot;:&quot;21317ebf-40d9-47bd-8ee2-8586e8df44b7&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_019nj9k8dfywD528JLjDFrys&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on 使用说明.md:\n     1\t# 卡钻检测使用说明\n     2\t\n     3\t## 目录结构\n     4\t\n     5\t使用本软件，需要按照以下目录结构准备数据：\n     6\t\n     7\t```\n     8\t./dataset/test/泸203H11-5/\n     9\t    ├── train自201H54-3.npy     # 训练数据文件（固定文件名）\n    10\t    ├── 实时数据1.npy           # 测试文件1\n    11\t    ├── 实时数据1_label.npy     # 测试文件1对应的标签文件（可选）\n    12\t    ├── 实时数据2.npy           # 测试文件2\n    13\t    └── 实时数据2_label.npy     # 测试文件2对应的标签文件（可选）\n    14\t```\n    15\t\n    16\t注意：\n    17\t- 测试文件可以有多个，命名不限\n    18\t- 标签文件必须遵循\&quot;测试文件名_label.npy\&quot;的命名规则\n    19\t- 标签文件为可选项（使用无标签模式时不需要）\n    20\t- 训练数据文件名为固定的\&quot;train自201H54-3.npy\&quot;\n    21\t\n    22\t## 测试方法\n    23\t\n    24\t### 单个文件测试\n    25\t\n    26\t#### 标准模式（使用标签）\n    27\t\n    28\t```bash\n    29\tpython run.py --task_name=anomaly_detection --is_training=0 --root_path=./dataset/test --data_path=泸203H11-5 --model_id=泸203H11-1 --model=FEDformer --data=anomaly_detection --features=M --seq_len=96 --label_len=48 --pred_len=96 --e_layers=3 --d_layers=1 --factor=5 --enc_in=12 --dec_in=12 --c_out=12 --d_model=128 --d_ff=512 --des=Exp --itr=1 --top_k=5 --test_file=实时数据2.npy --label_file=实时数据2_label.npy\n    30\t```\n    31\t\n    32\t#### 无标签模式\n    33\t\n    34\t```bash\n    35\tpython run.py --task_name=anomaly_detection --is_training=0 --root_path=./dataset/test --data_path=泸203H11-5 --model_id=泸203H11-1 --model=FEDformer --data=anomaly_detection --features=M --seq_len=96 --label_len=48 --pred_len=96 --e_layers=3 --d_layers=1 --factor=5 --enc_in=12 --dec_in=12 --c_out=12 --d_model=128 --d_ff=512 --des=Exp --itr=1 --top_k=5 --test_file=实时数据2.npy --no_label\n    36\t```\n    37\t\n    38\t### 批量测试\n    39\t\n    40\t#### 直接使用默认配置（推荐方式）\n    41\t\n    42\t直接编辑`batch_test.py`文件开头的配置部分，设置所需参数：\n    43\t\n    44\t```python\n    45\t# ============= 参数设置区域（直接修改这里的值） =============\n    46\t# 全局控制变量 - 标签模式设置\n    47\tUSE_LABEL = False  # True表示使用标签模式，False表示无标签模式\n    48\t\n    49\t# 默认参数配置（必填参数在此处设置，无需通过命令行指定）\n    50\tDEFAULT_CONFIG = {\n    51\t    \&quot;root_path\&quot;: \&quot;./dataset/test\&quot;,        # 数据根目录\n    52\t    \&quot;data_path\&quot;: \&quot;泸203H11-5\&quot;,            # 数据子目录\n    53\t    \&quot;model_id\&quot;: \&quot;泸203H11-1\&quot;,             # 模型ID\n    54\t    # 其他参数...\n    55\t}\n    56\t```\n    57\t\n    58\t然后直接运行（无需任何命令行参数）：\n    59\t\n    60\t```bash\n    61\tpython batch_test.py\n    62\t```\n    63\t\n    64\t#### 自动扫描测试文件（命令行方式）\n    65\t\n    66\t```bash\n    67\tpython batch_test.py --root_path=./dataset/test --data_path=泸203H11-5\n    68\t```\n    69\t\n    70\t#### 使用命令行切换无标签模式\n    71\t\n    72\t```bash\n    73\tpython batch_test.py --root_path=./dataset/test --data_path=泸203H11-5 --no_label\n    74\t```\n    75\t\n    76\t#### 指定匹配模式\n    77\t\n    78\t```bash\n    79\tpython batch_test.py --root_path=./dataset/test --data_path=泸203H11-5 --pattern=\&quot;实时*.npy\&quot;\n    80\t```\n    81\t\n    82\t## 参数说明\n    83\t\n    84\t| 参数 | 说明 | 默认值 |\n    85\t| --- | --- | --- |\n    86\t| `--root_path` | 数据根目录 | 配置文件中设置 |\n    87\t| `--data_path` | 数据子目录 | 配置文件中设置 |\n    88\t| `--model_id` | 模型ID | 配置文件中设置 |\n    89\t| `--test_file` | 测试文件名 | 无 |\n    90\t| `--label_file` | 标签文件名 | 无 |\n    91\t| `--no_label` | 无标签模式（不使用标签文件） | 由USE_LABEL控制 |\n    92\t| `--test_dir` | 指定测试文件目录 | 配置文件中设置 |\n    93\t| `--pattern` | 文件名匹配模式（如\&quot;*.npy\&quot;） | 配置文件中设置 |\n    94\t| `--exclude` | 排除的文件模式（用逗号分隔） | 配置文件中设置 |\n    95\t\n    96\t## 配置优先级\n    97\t\n    98\t1. 命令行参数（最高优先级）\n    99\t2. 代码中的配置（DEFAULT_CONFIG）\n   100\t3. 程序内部默认值（最低优先级）\n   101\t\n   102\t## 注意事项\n   103\t\n   104\t1. 训练数据文件名是固定的：`train自201H54-3.npy`\n   105\t2. 在无标签模式下，程序不会计算准确率、精确率等指标\n   106\t3. 结果文件名格式为：`anomaly_results_{测试文件名}_{时间戳}.csv`\n   107\t4. 编辑配置后可以直接运行`python batch_test.py`，无需提供任何命令行参数 \nTotal lines in file: 107\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;e5a499f9-9bc3-4939-8c57-c4a04333a4f8;toolu_vrtx_012REEj947xaSbaCS1VpmZzM&quot;:{&quot;requestId&quot;:&quot;e5a499f9-9bc3-4939-8c57-c4a04333a4f8&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_012REEj947xaSbaCS1VpmZzM&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on 预处理使用说明.md:\n     1\t# CSV数据预处理工具使用说明\n     2\t\n     3\t这个工具可以帮助您将CSV格式的钻井数据文件转换为适合模型使用的NPY格式，并根据卡钻时间记录自动生成标签文件。\n     4\t\n     5\t## 功能特点\n     6\t\n     7\t1. 自动将CSV文件转换为NPY格式\n     8\t2. 自动识别训练文件和测试文件\n     9\t3. 自动合并多个训练文件\n    10\t4. 根据卡钻时间记录生成标签\n    11\t5. 保留时间戳信息用于结果分析\n    12\t6. **生成符合批量测试格式要求的文件名**\n    13\t\n    14\t## 安装依赖\n    15\t\n    16\t```bash\n    17\tpip install numpy pandas tqdm\n    18\t```\n    19\t\n    20\t## 使用方法\n    21\t\n    22\t### 基本用法\n    23\t\n    24\t```bash\n    25\tpython preprocess_data.py --input_dir ./raw_data --output_dir ./dataset/test/泸203H11-5\n    26\t```\n    27\t\n    28\t### 带卡钻时间记录的用法\n    29\t\n    30\t```bash\n    31\tpython preprocess_data.py --input_dir ./raw_data --output_dir ./dataset/test/泸203H11-5 --stuck_file ./卡钻记录.csv\n    32\t```\n    33\t\n    34\t### 完整参数\n    35\t\n    36\t```bash\n    37\tpython preprocess_data.py --input_dir ./raw_data --output_dir ./dataset/test/泸203H11-5 --stuck_file ./卡钻记录.csv --time_col \&quot;时间\&quot; --start_col \&quot;卡钻开始时间\&quot; --end_col \&quot;卡钻结束时间\&quot; --time_format \&quot;%Y-%m-%d %H:%M:%S\&quot;\n    38\t```\n    39\t\n    40\t## 参数说明\n    41\t\n    42\t| 参数 | 说明 | 默认值 |\n    43\t|------|------|--------|\n    44\t| --input_dir | CSV文件所在目录 | (必填) |\n    45\t| --output_dir | NPY输出目录 | (必填) |\n    46\t| --stuck_file | 卡钻时间记录文件（CSV格式） | (可选) |\n    47\t| --time_col | 时间列名称 | \&quot;时间\&quot; |\n    48\t| --start_col | 卡钻开始时间列名称 | \&quot;卡钻开始时间\&quot; |\n    49\t| --end_col | 卡钻结束时间列名称 | \&quot;卡钻结束时间\&quot; |\n    50\t| --time_format | 时间格式 | \&quot;%Y-%m-%d %H:%M:%S\&quot; |\n    51\t\n    52\t## 卡钻时间记录文件格式\n    53\t\n    54\t卡钻时间记录文件应为CSV格式，至少包含两列：卡钻开始时间和卡钻结束时间。例如：\n    55\t\n    56\t```\n    57\t卡钻开始时间,卡钻结束时间\n    58\t2023-01-01 08:30:00,2023-01-01 09:15:00\n    59\t2023-01-02 14:20:00,2023-01-02 15:45:00\n    60\t```\n    61\t\n    62\t## 处理流程\n    63\t\n    64\t1. 自动识别训练文件和测试文件\n    65\t   - 文件名中包含\&quot;train\&quot;或\&quot;训练\&quot;的被识别为训练文件\n    66\t   - 文件名中包含\&quot;test\&quot;或\&quot;测试\&quot;的被识别为测试文件\n    67\t   - 其他文件会询问用户进行分类\n    68\t\n    69\t2. 处理训练文件\n    70\t   - 合并所有训练文件数据\n    71\t   - 保存为固定文件名\&quot;train自201H54-3.npy\&quot;\n    72\t\n    73\t3. 处理测试文件\n    74\t   - 单独处理每个测试文件\n    75\t   - 根据卡钻时间记录生成标签\n    76\t   - 保存为原文件名.npy和原文件名_label.npy\n    77\t   - **注意：标签文件会自动保存为\&quot;文件名_label.npy\&quot;格式，以配合批量测试功能**\n    78\t\n    79\t4. 数据格式\n    80\t   - NPY文件最后一列保存了时间戳信息\n    81\t   - 标签文件中，1表示卡钻，0表示正常\n    82\t\n    83\t## 批量处理流程\n    84\t\n    85\t1. 使用预处理工具处理原始CSV数据\n    86\t   ```bash\n    87\t   python preprocess_data.py --input_dir ./raw_data --output_dir ./dataset/test/泸203H11-5 --stuck_file ./卡钻记录.csv\n    88\t   ```\n    89\t\n    90\t2. 使用批量测试脚本进行测试\n    91\t   ```bash\n    92\t   python batch_test.py --root_path=./dataset/test --data_path=泸203H11-5\n    93\t   ```\n    94\t\n    95\t## 注意事项\n    96\t\n    97\t1. 确保CSV文件中包含足够的数值列作为特征\n    98\t2. 时间列必须存在且格式正确\n    99\t3. 卡钻时间记录文件中的时间应与数据文件中的时间格式一致\n   100\t4. 输出目录会自动创建，如已存在则会覆盖同名文件\n   101\t5. **标签文件命名格式为\&quot;原文件名_label.npy\&quot;，这是批量测试自动识别所必需的** \nTotal lines in file: 101\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;ebd424bd-e4a7-4602-a1fa-7d31594b9b21;toolu_vrtx_01RyvrbFKMUP1138kGGiZtvc&quot;:{&quot;requestId&quot;:&quot;ebd424bd-e4a7-4602-a1fa-7d31594b9b21&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01RyvrbFKMUP1138kGGiZtvc&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;The following code sections were retrieved:\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/batch_test.py\n     1\timport os\n     2\timport subprocess\n     3\timport argparse\n     4\timport glob\n     5\timport re\n     6\timport numpy as np\n     7\tfrom sklearn.preprocessing import StandardScaler\n     8\timport time\n     9\timport io\n    10\timport sys\n    11\tfrom collections import defaultdict\n    12\t\n    13\t# ============= 参数设置区域（直接修改这里的值） =============\n    14\t# 全局控制变量 - 标签模式设置\n    15\tUSE_LABEL = False  # True表示使用标签模式，False表示无标签模式\n    16\t\n    17\t# 默认参数配置（必填参数在此处设置，无需通过命令行指定）\n    18\tDEFAULT_CONFIG = {\n    19\t    \&quot;root_path\&quot;: \&quot;./dataset/test\&quot;,        # 数据根目录\n    20\t    \&quot;data_path\&quot;: \&quot;泸203H11-5\&quot;,            # 数据子目录\n    21\t    \&quot;model_id\&quot;: \&quot;泸203H11-2\&quot;,             # 模型ID\n    22\t    \&quot;model\&quot;: \&quot;FEDformer\&quot;,                 # 模型名称\n    23\t    \&quot;seq_len\&quot;: 96,                        # 序列长度\n    24\t    \&quot;label_len\&quot;: 48,                      # 标签长度\n    25\t    \&quot;pred_len\&quot;: 96,                       # 预测长度\n    26\t    \&quot;test_dir\&quot;: \&quot;./dataset/505\&quot;,         # 测试文件目录，不指定则使用root_path/data_path\n    27\t    \&quot;pattern\&quot;: None,                      # 文件名匹配模式，如 \&quot;*.npy\&quot;\n    28\t    \&quot;exclude\&quot;: \&quot;*_label.npy,train*.npy\&quot;   # 排除的文件模式，用逗号分隔\n    29\t}\n    30\t# =========================================================\n    31\t\n    32\t# 全局缓存，用于存储已加载和处理的数据\n    33\tCACHE = {\n    34\t    'scaler': None,           # 训练好的StandardScaler\n    35\t    'train_data': None,       # 处理后的训练数据\n    36\t    'train_data_loaded': False # 标记是否已加载训练数据\n    37\t}\n    38\t\n    39\tdef extract_anomaly_ratio(output_text):\n    40\t    \&quot;\&quot;\&quot;从输出文本中提取经验卡钻检测异常比例\&quot;\&quot;\&quot;\n    41\t    # 使用正则表达式查找\&quot;经验卡钻检测异常比例: xx.xx%\&quot;\n    42\t    match = re.search(r'经验卡钻检测异常比例: (\\d+\\.\\d+)%', output_text)\n    43\t    if match:\n    44\t        return float(match.group(1))\n    45\t    return None\n    46\t\n    47\tdef ensure_cache_dir():\n    48\t    \&quot;\&quot;\&quot;确保缓存目录存在\&quot;\&quot;\&quot;\n    49\t    cache_dir = './.cache'\n    50\t    if not os.path.exists(cache_dir):\n    51\t        os.makedirs(cache_dir)\n    52\t        print(f\&quot;创建缓存目录: {cache_dir}\&quot;)\n    53\t    return cache_dir\n    54\t\n    55\tdef preprocess_train_data(root_path):\n    56\t    \&quot;\&quot;\&quot;预处理训练数据，避免重复加载和处理\&quot;\&quot;\&quot;\n    57\t    if CACHE['train_data_loaded']:\n    58\t        print(\&quot;使用缓存的训练数据和标准化器\&quot;)\n    59\t        return CACHE['scaler'], CACHE['train_data']\n    60\t    \n    61\t    # 确保缓存目录存在\n    62\t    cache_dir = ensure_cache_dir()\n    63\t    cache_path = os.path.join(cache_dir, 'train_data_cache.npz')\n    64\t    \n    65\t    # 检查是否存在磁盘缓存\n    66\t    if os.path.exists(cache_path):\n    67\t        print(f\&quot;从磁盘缓存加载训练数据: {cache_path}\&quot;)\n    68\t        try:\n    69\t            cache_data = np.load(cache_path, allow_pickle=True)\n    70\t            CACHE['train_data'] = cache_data['train_data']\n    71\t            CACHE['scaler'] = cache_data['scaler'].item()  # 将scaler从数组转回对象\n    72\t            CACHE['train_data_loaded'] = True\n    73\t            print(\&quot;成功从磁盘缓存加载数据\&quot;)\n    74\t            return CACHE['scaler'], CACHE['train_data']\n    75\t        except Exception as e:\n    76\t            print(f\&quot;读取缓存文件失败: {e}，将重新处理训练数据\&quot;)\n    77\t    \n    78\t    print(\&quot;第一次加载训练数据，进行预处理...\&quot;)\n    79\t    start_time = time.time()\n    80\t    \n    81\t    # 尝试加载训练数据\n    82\t    try:\n    83\t        # 首先尝试从当前根目录加载训练数据\n    84\t        train_data_path = os.path.join(root_path, \&quot;train自201H54-3.npy\&quot;)\n    85\t        if not os.path.exists(train_data_path):\n    86\t            # 如果找不到，则尝试从标准位置加载\n    87\t            default_train_path = os.path.join(\&quot;./dataset/test\&quot;, \&quot;train自201H54-3.npy\&quot;)\n    88\t            if os.path.exists(default_train_path):\n    89\t                train_data_path = default_train_path\n    90\t                print(f\&quot;从默认位置加载训练数据: {default_train_path}\&quot;)\n    91\t            else:\n    92\t                raise FileNotFoundError(f\&quot;找不到训练数据，已尝试路径: {train_data_path}, {default_train_path}\&quot;)\n    93\t            \n    94\t        train_data = np.load(train_data_path, allow_pickle=True)\n    95\t        print(f\&quot;成功加载训练数据: {train_data_path}\&quot;)\n    96\t    except Exception as e:\n    97\t        print(f\&quot;加载训练数据失败: {e}\&quot;)\n    98\t        # 创建一个假的训练数据，避免程序崩溃\n    99\t        train_data = np.zeros((100, 13))\n   100\t        print(\&quot;使用空训练数据代替\&quot;)\n   101\t    \n   102\t    # 预处理训练数据\n   103\t    train_data = train_data[:, :-1]  # 去除最后一列\n   104\t    scaler = StandardScaler()\n   105\t    scaler.fit(train_data)\n   106\t    \n   107\t    # 存入缓存\n   108\t    CACHE['scaler'] = scaler\n   109\t    CACHE['train_data'] = train_data\n   110\t    CACHE['train_data_loaded'] = True\n   111\t    \n   112\t    # 将预处理数据保存到磁盘缓存\n   113\t    try:\n   114\t        print(f\&quot;保存训练数据到磁盘缓存: {cache_path}\&quot;)\n   115\t        np.savez(cache_path, train_data=train_data, scaler=np.array([scaler], dtype=object))\n   116\t        print(\&quot;缓存保存成功\&quot;)\n   117\t    except Exception as e:\n   118\t        print(f\&quot;保存缓存失败: {e}\&quot;)\n   119\t    \n   120\t    elapsed = time.time() - start_time\n   121\t    print(f\&quot;训练数据预处理完成，耗时: {elapsed:.2f}秒\&quot;)\n   122\t    \n   123\t    return scaler, train_data\n   124\t\n   125\tdef parse_args():\n   126\t    parser = argparse.ArgumentParser(description='批量异常检测测试')\n   127\t    # 使用默认参数配置，但允许命令行参数覆盖\n   128\t    parser.add_argument('--root_path', type=str, default=DEFAULT_CONFIG[\&quot;root_path\&quot;], help='数据根目录')\n   129\t    parser.add_argument('--model_id', type=str, default=DEFAULT_CONFIG[\&quot;model_id\&quot;], help='模型ID')\n   130\t    parser.add_argument('--model', type=str, default=DEFAULT_CONFIG[\&quot;model\&quot;], help='模型名称')\n   131\t    parser.add_argument('--seq_len', type=int, default=DEFAULT_CONFIG[\&quot;seq_len\&quot;], help='序列长度')\n   132\t    parser.add_argument('--label_len', type=int, default=DEFAULT_CONFIG[\&quot;label_len\&quot;], help='标签长度')\n   133\t    parser.add_argument('--pred_len', type=int, default=DEFAULT_CONFIG[\&quot;pred_len\&quot;], help='预测长度')\n   134\t    parser.add_argument('--test_dir', type=str, default=DEFAULT_CONFIG[\&quot;test_dir\&quot;], help='测试文件目录，如不指定则使用root_path/data_path')\n   135\t    parser.add_argument('--data_path', type=str, default=DEFAULT_CONFIG[\&quot;data_path\&quot;], help='数据子目录')\n   136\t    parser.add_argument('--pattern', type=str, default=DEFAULT_CONFIG[\&quot;pattern\&quot;], help='文件名匹配模式，例如\&quot;*.npy\&quot;')\n   137\t    parser.add_argument('--exclude', type=str, default=DEFAULT_CONFIG[\&quot;exclude\&quot;], help='排除的文件模式，用逗号分隔')\n   138\t    parser.add_argument('--no_label', action='store_true', help='不使用标签文件')\n   139\t    parser.add_argument('--save_summary', action='store_true', help='保存汇总结果到CSV文件')\n   140\t    \n   141\t    # 如果没有提供命令行参数，使用默认配置\n   142\t    if len(os.sys.argv) == 1:\n   143\t        print(\&quot;使用默认配置运行...\&quot;)\n   144\t        print(f\&quot;数据目录: {DEFAULT_CONFIG['test_dir'] if DEFAULT_CONFIG['test_dir'] else DEFAULT_CONFIG['root_path'] + '/' + DEFAULT_CONFIG['data_path']}\&quot;)\n   145\t        print(f\&quot;标签模式: {'' if USE_LABEL else '无'}标签模式\&quot;)\n   146\t        args = parser.parse_args([])\n   147\t    else:\n   148\t        args = parser.parse_args()\n   149\t    return args\n   150\t\n   151\tdef find_test_files(test_dir, pattern=None, exclude_patterns=None, no_label=False):\n   152\t    \&quot;\&quot;\&quot;\n   153\t    查找测试文件及其对应的标签文件\n   154\t    \n   155\t    参数:\n   156\t    - test_dir: 测试文件目录\n   157\t    - pattern: 文件匹配模式（如 \&quot;*.npy\&quot;）\n   158\t    - exclude_patterns: 要排除的文件模式列表\n   159\t    - no_label: 是否不使用标签文件\n   160\t    \n   161\t    返回:\n   162\t    - 包含测试文件和标签文件的字典列表\n   163\t    \&quot;\&quot;\&quot;\n   164\t    if pattern is None:\n   165\t        pattern = \&quot;*.npy\&quot;  # 默认查找所有.npy文件\n   166\t    \n   167\t    # 转换排除模式为列表\n   168\t    if exclude_patterns is None:\n   169\t        exclude_patterns = [\&quot;*_label.npy\&quot;, \&quot;train*.npy\&quot;]  # 默认排除标签文件和训练文件\n   170\t    elif isinstance(exclude_patterns, str):\n   171\t        exclude_patterns = [p.strip() for p in exclude_patterns.split(',')]\n   172\t    \n   173\t    # 确保测试目录存在\n   174\t    if not os.path.exists(test_dir):\n   175\t        print(f\&quot;错误: 测试目录 '{test_dir}' 不存在!\&quot;)\n   176\t        return []\n   177\t        \n   178\t    # 查找所有符合模式的文件\n   179\t    all_files = glob.glob(os.path.join(test_dir, pattern))\n   180\t    \n   181\t    # 排除匹配排除模式的文件\n   182\t    for exclude_pattern in exclude_patterns:\n   183\t        exclude_files = glob.glob(os.path.join(test_dir, exclude_pattern))\n   184\t        all_files = [f for f in all_files if f not in exclude_files]\n   185\t    \n   186\t    test_datasets = []\n   187\t    \n   188\t    for test_file in all_files:\n   189\t        file_name = os.path.basename(test_file)\n   190\t        file_base, file_ext = os.path.splitext(file_name)\n   191\t        \n   192\t        if no_label:\n   193\t            # 不使用标签文件，直接添加测试文件\n   194\t            test_datasets.append({\n   195\t                \&quot;name\&quot;: file_base,\n   196\t                \&quot;test_file\&quot;: file_name,\n   197\t                \&quot;label_file\&quot;: \&quot;dummy_label.npy\&quot;  # 使用虚拟标签文件名\n   198\t            })\n   199\t        else:\n   200\t            # 构建预期的标签文件名\n   201\t            label_file = os.path.join(test_dir, f\&quot;{file_base}_label{file_ext}\&quot;)\n   202\t            \n   203\t            # 检查标签文件是否存在\n   204\t            if os.path.exists(label_file):\n   205\t                test_datasets.append({\n   206\t                    \&quot;name\&quot;: file_base,\n   207\t                    \&quot;test_file\&quot;: file_name,\n   208\t                    \&quot;label_file\&quot;: os.path.basename(label_file)\n   209\t                })\n   210\t            else:\n   211\t                print(f\&quot;警告: 未找到文件 '{test_file}' 对应的标签文件 '{label_file}'\&quot;)\n   212\t    \n   213\t    return test_datasets\n   214\t\n   215\tdef save_anomaly_summary(anomaly_ratios, filename=\&quot;卡钻异常比例汇总.csv\&quot;):\n   216\t    \&quot;\&quot;\&quot;将异常比例汇总保存到CSV文件\&quot;\&quot;\&quot;\n   217\t    import csv\n   218\t    \n   219\t    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:\n   220\t        writer = csv.writer(csvfile)\n   221\t        writer.writerow(['文件名', '经验卡钻检测异常比例(%)', '是否使用经验卡钻检测'])\n   222\t        \n   223\t        for file_name, data in anomaly_ratios.items():\n   224\t            ratio = data.get('ratio', 'N/A')\n   225\t            is_valid = data.get('valid', False)\n   226\t            if ratio != 'N/A':\n   227\t                writer.writerow([file_name, f\&quot;{ratio:.2f}\&quot;, \&quot;是\&quot; if is_valid else \&quot;否\&quot;])\n   228\t            else:\n   229\t                writer.writerow([file_name, \&quot;N/A\&quot;, \&quot;否\&quot;])\n   230\t    \n   231\t    print(f\&quot;异常比例汇总已保存到 {filename}\&quot;)\n   232\t\n   233\tdef main():\n   234\t    args = parse_args()\n   235\t    \n   236\t    # 确保缓存目录存在\n   237\t    ensure_cache_dir()\n   238\t    \n   239\t    # 使用全局控制变量覆盖命令行参数\n   240\t    args.no_label = not USE_LABEL\n   241\t    \n   242\t    # 确定测试目录\n   243\t    test_dir = args.test_dir if args.test_dir else os.path.join(args.root_path, args.data_path)\n   244\t    \n   245\t    # 预处理训练数据，只加载一次\n   246\t    print(\&quot;预处理训练数据...\&quot;)\n   247\t    scaler, train_data = preprocess_train_data(test_dir)\n   248\t    \n   249\t    # 查找测试文件\n   250\t    test_datasets = find_test_files(test_dir, args.pattern, args.exclude, args.no_label)\n   251\t    \n   252\t    if not test_datasets:\n   253\t        print(f\&quot;未在 {test_dir} 中找到有效的测试文件。\&quot;)\n   254\t        return\n   255\t    \n   256\t    print(f\&quot;找到 {len(test_datasets)} 个测试文件:\&quot;)\n   257\t    for i, dataset in enumerate(test_datasets):\n   258\t        if args.no_label:\n   259\t            print(f\&quot;{i+1}. {dataset['name']}: {dataset['test_file']} (无标签模式)\&quot;)\n   260\t        else:\n   261\t            print(f\&quot;{i+1}. {dataset['name']}: {dataset['test_file']} -&gt; {dataset['label_file']}\&quot;)\n   262\t    \n   263\t    # 存储每个文件的经验卡钻检测异常比例\n   264\t    anomaly_ratios = {}\n   265\t    \n   266\t    # 批量执行测试\n   267\t    total_start_time = time.time()\n   268\t    \n   269\t    for idx, dataset in enumerate(test_datasets):\n   270\t        print(f\&quot;\\n开始测试 ({idx+1}/{len(test_datasets)}): {dataset['name']}\&quot;)\n   271\t        test_start_time = time.time()\n   272\t        \n   273\t        # 构建测试文件的完整路径 - 使用测试目录中的文件\n   274\t        test_file_path = os.path.join(test_dir, dataset['test_file'])\n   275\t        \n   276\t        cmd = [\n   277\t            \&quot;python\&quot;, \&quot;run.py\&quot;,\n   278\t            \&quot;--task_name=anomaly_detection\&quot;,\n   279\t            \&quot;--is_training=0\&quot;,\n   280\t            f\&quot;--root_path={test_dir}\&quot;,  # 使用test_dir作为根目录\n   281\t            f\&quot;--data_path={args.data_path}\&quot;,\n   282\t            f\&quot;--model_id={args.model_id}\&quot;,\n   283\t            f\&quot;--model={args.model}\&quot;,\n   284\t            \&quot;--data=anomaly_detection\&quot;,\n   285\t            \&quot;--features=M\&quot;,\n   286\t            f\&quot;--seq_len={args.seq_len}\&quot;,\n   287\t            f\&quot;--label_len={args.label_len}\&quot;,\n   288\t            f\&quot;--pred_len={args.pred_len}\&quot;,\n   289\t            \&quot;--e_layers=3\&quot;,\n   290\t            \&quot;--d_layers=1\&quot;,\n   291\t            \&quot;--factor=5\&quot;,\n   292\t            \&quot;--enc_in=12\&quot;,\n   293\t            \&quot;--dec_in=12\&quot;,\n   294\t            \&quot;--c_out=12\&quot;,\n   295\t            \&quot;--d_model=128\&quot;,\n   296\t            \&quot;--d_ff=512\&quot;,\n   297\t            \&quot;--des='Exp'\&quot;,\n   298\t            \&quot;--itr=1\&quot;,\n   299\t            \&quot;--top_k=5\&quot;,\n   300\t            f\&quot;--test_file={dataset['test_file']}\&quot;,  # 只传文件名，不包含路径\n   301\t        ]\n   302\t        \n   303\t        # 根据全局控制变量决定是否使用标签\n   304\t        if not args.no_label:\n   305\t            cmd.append(f\&quot;--label_file={dataset['label_file']}\&quot;)\n   306\t        else:\n   307\t            cmd.append(\&quot;--no_label\&quot;)\n   308\t        \n   309\t        # 使用环境变量传递缓存信息，告诉数据加载器使用已处理的数据\n   310\t        env = os.environ.copy()\n   311\t        env['USE_CACHED_TRAIN_DATA'] = 'true'\n   312\t        env['CACHE_DIR'] = './.cache'\n   313\t        \n   314\t        # 正确捕获子进程输出 - 使用 Popen 和管道而不是 OutputCapture 类\n   315\t        try:\n   316\t#            print(f\&quot;执行命令: {' '.join(cmd)}\&quot;)\n   317\t            process = subprocess.Popen(\n   318\t                cmd, \n   319\t                env=env, \n   320\t                stdout=subprocess.PIPE,\n   321\t                stderr=subprocess.STDOUT,\n   322\t                universal_newlines=True,\n   323\t                bufsize=1\n   324\t            )\n   325\t            \n   326\t            # 收集所有输出\n   327\t            output_lines = []\n   328\t            for line in process.stdout:\n   329\t                print(line, end='')  # 实时显示输出\n   330\t                output_lines.append(line)\n   331\t            \n   332\t            # 等待进程完成\n   333\t            process.wait()\n   334\t            \n   335\t            # 全部输出文本\n   336\t            output_text = ''.join(output_lines)\n   337\t            \n   338\t            # 提取异常比例\n   339\t            anomaly_ratio = extract_anomaly_ratio(output_text)\n   340\t            \n   341\t            # 判断是否使用了经验卡钻检测方法\n   342\t            is_valid_ratio = False\n   343\t            if anomaly_ratio is not None:\n   344\t                if \&quot;使用经验卡钻检测结果\&quot; in output_text:\n   345\t                    is_valid_ratio = True\n   346\t                \n   347\t                # 将结果存入字典\n   348\t                anomaly_ratios[dataset['name']] = {\n   349\t                    'ratio': anomaly_ratio, \n   350\t                    'valid': is_valid_ratio\n   351\t                }\n   352\t                \n   353\t                print(f\&quot;提取到经验卡钻检测异常比例: {anomaly_ratio:.2f}%，{'使用' if is_valid_ratio else '未使用'}经验卡钻检测结果\&quot;)\n   354\t            else:\n   355\t                print(\&quot;未能提取到经验卡钻检测异常比例\&quot;)\n   356\t                anomaly_ratios[dataset['name']] = {'ratio': 'N/A', 'valid': False}\n   357\t                \n   358\t        except Exception as e:\n   359\t            print(f\&quot;执行测试命令失败: {e}\&quot;)\n   360\t            anomaly_ratios[dataset['name']] = {'ratio': 'N/A', 'valid': False}\n   361\t        \n   362\t        test_elapsed = time.time() - test_start_time\n   363\t        print(f\&quot;完成测试: {dataset['name']}, 耗时: {test_elapsed:.2f}秒\&quot;)\n   364\t    \n   365\t    total_elapsed = time.time() - total_start_time\n   366\t    print(f\&quot;\\n所有测试完成，总耗时: {total_elapsed:.2f}秒，平均每个测试: {total_elapsed/len(test_datasets):.2f}秒\&quot;)\n   367\t\n   368\t    # 打印汇总报告\n   369\t    print(\&quot;\\n===== 经验卡钻检测异常比例汇总 =====\&quot;)\n   370\t    print(f\&quot;{'文件名':&lt;30} {'异常比例(%)':&lt;15} {'是否使用'}\&quot;)\n   371\t    print(\&quot;-\&quot; * 60)\n   372\t    \n   373\t    for file_name, data in anomaly_ratios.items():\n   374\t        ratio = data.get('ratio', 'N/A')\n   375\t        is_valid = data.get('valid', False)\n   376\t        if ratio != 'N/A':\n   377\t            print(f\&quot;{file_name:&lt;30} {ratio:&gt;8.2f}%      {'是' if is_valid else '否'}\&quot;)\n   378\t        else:\n   379\t            print(f\&quot;{file_name:&lt;30} {'N/A':&lt;15} 否\&quot;)\n   380\t    \n   381\t    print(\&quot;-\&quot; * 60)\n   382\t    \n   383\t    # 保存汇总到CSV文件\n   384\t    if args.save_summary or True:  # 默认总是保存\n   385\t        save_anomaly_summary(anomaly_ratios)\n   386\t\n   387\tif __name__ == \&quot;__main__\&quot;:\n   388\t    main() ...\nPath: 使用说明.md\n...\n    33\t\n    34\t```bash\n    35\tpython run.py --task_name=anomaly_detection --is_training=0 --root_path=./dataset/test --data_path=泸203H11-5 --model_id=泸203H11-1 --model=FEDformer --data=anomaly_detection --features=M --seq_len=96 --label_len=48 --pred_len=96 --e_layers=3 --d_layers=1 --factor=5 --enc_in=12 --dec_in=12 --c_out=12 --d_model=128 --d_ff=512 --des=Exp --itr=1 --top_k=5 --test_file=实时数据2.npy --no_label\n    36\t```\n    37\t\n    38\t### 批量测试\n    39\t\n    40\t#### 直接使用默认配置（推荐方式）\n    41\t\n    42\t直接编辑`batch_test.py`文件开头的配置部分，设置所需参数：\n    43\t\n    44\t```python\n    45\t# ============= 参数设置区域（直接修改这里的值） =============\n    46\t# 全局控制变量 - 标签模式设置\n    47\tUSE_LABEL = False  # True表示使用标签模式，False表示无标签模式\n    48\t\n    49\t# 默认参数配置（必填参数在此处设置，无需通过命令行指定）\n    50\tDEFAULT_CONFIG = {\n    51\t    \&quot;root_path\&quot;: \&quot;./dataset/test\&quot;,        # 数据根目录\n    52\t    \&quot;data_path\&quot;: \&quot;泸203H11-5\&quot;,            # 数据子目录\n    53\t    \&quot;model_id\&quot;: \&quot;泸203H11-1\&quot;,             # 模型ID\n    54\t    # 其他参数...\n    55\t}\n    56\t```\n    57\t\n    58\t然后直接运行（无需任何命令行参数）：\n    59\t\n    60\t```bash\n    61\tpython batch_test.py\n    62\t```\n    63\t\n    64\t#### 自动扫描测试文件（命令行方式）\n    65\t\n    66\t```bash\n    67\tpython batch_test.py --root_path=./dataset/test --data_path=泸203H11-5\n    68\t```\n    69\t\n    70\t#### 使用命令行切换无标签模式\n    71\t\n    72\t```bash\n    73\tpython batch_test.py --root_path=./dataset/test --data_path=泸203H11-5 --no_label\n    74\t```\n    75\t\n    76\t#### 指定匹配模式\n    77\t\n    78\t```bash\n    79\tpython batch_test.py --root_path=./dataset/test --data_path=泸203H11-5 --pattern=\&quot;实时*.npy\&quot;\n    80\t```\n    81\t\n    82\t## 参数说明\n...\nPath: 预处理使用说明.md\n...\n    78\t\n    79\t4. 数据格式\n    80\t   - NPY文件最后一列保存了时间戳信息\n    81\t   - 标签文件中，1表示卡钻，0表示正常\n    82\t\n    83\t## 批量处理流程\n    84\t\n    85\t1. 使用预处理工具处理原始CSV数据\n    86\t   ```bash\n    87\t   python preprocess_data.py --input_dir ./raw_data --output_dir ./dataset/test/泸203H11-5 --stuck_file ./卡钻记录.csv\n    88\t   ```\n    89\t\n    90\t2. 使用批量测试脚本进行测试\n    91\t   ```bash\n    92\t   python batch_test.py --root_path=./dataset/test --data_path=泸203H11-5\n    93\t   ```\n    94\t\n    95\t## 注意事项\n    96\t\n    97\t1. 确保CSV文件中包含足够的数值列作为特征\n    98\t2. 时间列必须存在且格式正确\n    99\t3. 卡钻时间记录文件中的时间应与数据文件中的时间格式一致\n   100\t4. 输出目录会自动创建，如已存在则会覆盖同名文件\n   101\t5. **标签文件命名格式为\&quot;原文件名_label.npy\&quot;，这是批量测试自动识别所必需的** ...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/exp/exp_anomaly_detection.py\n...\n   483\t        \n   484\t        postprocess_time = time.time() - postprocess_start\n   485\t        print(f\&quot;[性能统计] 后处理和评估耗时: {postprocess_time:.2f}秒\&quot;)\n   486\t\n   487\t        # 7. 结果保存\n   488\t        save_start = time.time()\n   489\t        # (5) 保存结果\n   490\t        batch_size = self.args.seq_len  # 假设 seq_len 是窗口大小\n   491\t        sampled_indices = np.arange(0, len(test_timestamps), batch_size)\n   492\t\n   493\t        # 生成包含时间戳和测试文件名的唯一结果文件名\n   494\t        timestamp_str = time.strftime(\&quot;%Y%m%d-%H%M%S\&quot;)\n   495\t        test_file_name = getattr(self.args, 'test_file', 'default').replace('.npy', '').replace('.', '_')\n   496\t        result_file_name = f'anomaly_results_{test_file_name}_{timestamp_str}.csv'\n   497\t\n   498\t        # 确保pred是整数类型的0和1\n   499\t        pred = pred.astype(int)\n...\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;c9ee79d6-e85f-4b4c-b679-2566e49f33fd;toolu_vrtx_01G8zeQmke8ruDwYEqt2Aqmj&quot;:{&quot;requestId&quot;:&quot;c9ee79d6-e85f-4b4c-b679-2566e49f33fd&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01G8zeQmke8ruDwYEqt2Aqmj&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;The following code sections were retrieved:\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/data_processing/可视化/可视化.py\n     1\timport pandas as pd\n     2\timport matplotlib.pyplot as plt\n     3\timport numpy as np\n     4\t\n     5\t# 读取CSV文件\n     6\tdf = pd.read_csv('长宁H23-2实时数据.csv')\n     7\t\n     8\t# 将时间列转换为datetime类型，并设置为索引\n     9\tdf['date'] = pd.to_datetime(df['date'])\n    10\tdf.set_index('date', inplace=True)\n    11\t\n    12\t# 获取特征列名（排除sfkz）\n    13\tfeatures = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP']\n    14\t\n    15\t# 手动指定每个特征的颜色\n    16\tcolors = [\n    17\t    'blue', 'green', 'red', 'purple', 'orange',\n    18\t    'brown', 'pink', 'gray', 'cyan', 'magenta'\n    19\t]\n...\n    41\t\n    42\t# 绘制每个特征的折线图\n    43\tfor i, (feature, color) in enumerate(zip(features, colors)):\n    44\t    ax = axes[i]\n    45\t    ax.plot(df.index, df[feature], label=feature, color=color)  # 使用指定颜色绘制折线图\n    46\t\n    47\t    # 标记特征sfkz为1的值，使用透明的黄色表示\n    48\t    sfkz_mask = df['sfkz'] == 1\n    49\t    ax.fill_between(df.index, df[feature].min(), df[feature].max(), where=sfkz_mask, color='yellow', alpha=0.3)\n    50\t\n    51\t    # 高亮显示连续长度超过60的区域\n    52\t    segments = highlight_long_segments(df.index, sfkz_mask.values, min_length=50)  # 使用sfkz_mask.values来确保是一个numpy数组\n    53\t    for start, end in segments:\n    54\t        # 高亮显示区域\n    55\t        ax.axvspan(df.index[start], df.index[end - 1], color='red', alpha=0.2)  # 调整红色的透明度为0.2\n    56\t\n    57\t        # 格式化时间段\n    58\t        segment_str = f'{df.index[start]} to {df.index[end - 1]}'\n    59\t        if segment_str not in printed_segments:\n    60\t            printed_segments.add(segment_str)\n    61\t            print(f'高风险 from {df.index[start]} to {df.index[end - 1]}')\n    62\t\n    63\t    ax.set_ylabel(feature)\n    64\t    ax.legend()\n    65\t\n    66\t# 设置整体标题和横坐标标签\n    67\tplt.suptitle('Time Series of Features')\n    68\tplt.xlabel('Date')\n    69\t\n    70\t# 调整布局，避免重叠\n    71\tplt.tight_layout(rect=[0, 0, 1, 0.95])\n    72\t\n    73\t# 显示图形\n    74\tplt.show()\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/utils/tools.py\n...\n    79\t\n    80\t\n    81\tdef visual(true, preds=None, name='./pic/test.pdf'):\n    82\t    \&quot;\&quot;\&quot;\n    83\t    Results visualization\n    84\t    \&quot;\&quot;\&quot;\n    85\t    plt.figure()\n    86\t    plt.plot(true, label='GroundTruth', linewidth=2)\n    87\t    if preds is not None:\n    88\t        plt.plot(preds, label='Prediction', linewidth=2)\n    89\t    plt.legend()\n    90\t    plt.savefig(name, bbox_inches='tight')\n    91\t\n    92\t\n    93\tdef adjustment(gt, pred):\n    94\t    anomaly_state = False\n    95\t    for i in range(len(gt)):\n    96\t        if gt[i] == 1 and pred[i] == 1 and not anomaly_state:\n    97\t            anomaly_state = True\n    98\t            for j in range(i, 0, -1):\n    99\t                if gt[j] == 0:\n   100\t                    break\n   101\t                else:\n   102\t                    if pred[j] == 0:\n   103\t                        pred[j] = 1\n   104\t            for j in range(i, len(gt)):\n   105\t                if gt[j] == 0:\n   106\t                    break\n   107\t                else:\n   108\t                    if pred[j] == 0:\n   109\t                        pred[j] = 1\n   110\t        elif gt[i] == 0:\n   111\t            anomaly_state = False\n   112\t        if anomaly_state:\n   113\t            pred[i] = 1\n   114\t    return gt, pred\n   115\t\n   116\t\n   117\tdef cal_accuracy(y_pred, y_true):\n   118\t    return np.mean(y_pred == y_true)...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/data_processing/可视化/可视化2.py\n...\n    41\t\n    42\t# 绘制每个特征的折线图\n    43\tfor i, (feature, color) in enumerate(zip(features, colors)):\n    44\t    ax = axes[i]\n    45\t    ax.plot(df.index, df[feature], label=feature, color=color)  # 使用指定颜色绘制折线图\n    46\t\n    47\t    # 标记特征sfkz为1的值，使用透明的黄色表示\n    48\t    sfkz_mask = df['sfkz'] == 1\n    49\t    ax.fill_between(df.index, df[feature].min(), df[feature].max(), where=sfkz_mask, color='yellow', alpha=0.3)\n    50\t\n    51\t    # 高亮显示连续长度超过60的区域\n    52\t    segments = highlight_long_segments(df.index, sfkz_mask.values, min_length=60)  # 使用sfkz_mask.values来确保是一个numpy数组\n    53\t    for start, end in segments:\n    54\t        # 高亮显示区域\n    55\t        ax.axvspan(df.index[start], df.index[end - 1], color='red', alpha=0.2)  # 调整红色的透明度为0.2\n    56\t\n    57\t        # 格式化时间段\n    58\t        segment_str = f'{df.index[start]} to {df.index[end - 1]}'\n    59\t        if segment_str not in printed_segments:\n    60\t            printed_segments.add(segment_str)\n    61\t            print(f'高风险 from {df.index[start]} to {df.index[end - 1]}')\n    62\t\n    63\t    ax.set_ylabel(feature)\n    64\t    ax.legend()\n    65\t\n    66\t# 设置整体标题和横坐标标签\n    67\tplt.suptitle('Time Series of Features')\n    68\tplt.xlabel('Date')\n    69\t\n    70\t# 调整布局，避免重叠\n    71\tplt.tight_layout(rect=[0, 0, 1, 0.95])\n    72\t\n    73\t# 显示图形\n    74\tplt.show()\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/data_processing/可视化_特征/可视化_特征.py\n     1\t# 读取CSV文件\n     2\tfile_path = '宁216H5-3实时数据.csv'  # 替换为你的文件路径\n     3\tdf = pd.read_csv(file_path)\n     4\t\n     5\t# 将'date'列设置为时间列，并转换为适当的时间格式（如果需要）\n     6\tdf['date'] = pd.to_datetime(df['date'])\n     7\t\n     8\t# 将DEP列转换为浮点数，以防数据类型不正确\n     9\tdf['DEP'] = pd.to_numeric(df['DEP'], errors='coerce')\n    10\t\n    11\t# 特征列\n    12\tfeatures = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP']\n    13\t\n    14\t# 设置子图布局\n    15\tnum_features = len(features)\n    16\tfig, axes = plt.subplots(num_features, 1, figsize=(10, 2 * num_features), sharex=True)\n    17\t\n    18\t# 使用matplotlib的颜色映射\n    19\tcolors = plt.get_cmap('tab10')\n    20\t\n    21\t# 绘制每个特征的时间变化曲线\n    22\tfor i, feature in enumerate(features):\n    23\t    axes[i].plot(df['date'], df[feature], label=feature, color=colors(i % 10))  # 通过索引选择颜色\n    24\t    axes[i].set_ylabel(feature)\n    25\t    axes[i].legend(loc='upper right')\n    26\t    axes[i].grid(True)\n    27\t\n    28\t    # 禁用科学计数法\n    29\t    axes[i].yaxis.set_major_formatter(ScalarFormatter(useOffset=False))\n    30\t    axes[i].ticklabel_format(style='plain', axis='y')  # 禁用科学计数法\n    31\t\n    32\t# 设置共享的X轴标签\n    33\taxes[-1].set_xlabel('Time')\n    34\t\n    35\t# 设置图表标题\n    36\tfig.suptitle('Feature Trends Over Time', y=1.02)\n    37\t\n    38\t# 调整布局\n    39\tplt.tight_layout()\n    40\tplt.show()\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/exp/exp_anomaly_detection.py\n...\n   157\t\n   158\t    def _save_anomaly_ratio_summary(self):\n   159\t        \&quot;\&quot;\&quot;保存所有异常比例汇总到CSV文件\&quot;\&quot;\&quot;\n   160\t        global ANOMALY_RATIO_SUMMARY, SUMMARY_FILE\n   161\t        \n   162\t        # 如果汇总文件已存在，尝试先读取已有数据\n   163\t        existing_data = {}\n   164\t        if os.path.exists(SUMMARY_FILE):\n   165\t            try:\n   166\t                with open(SUMMARY_FILE, 'r', newline='', encoding='utf-8-sig') as f:\n   167\t                    reader = csv.reader(f)\n   168\t                    headers = next(reader)  # 跳过表头\n   169\t                    for row in reader:\n   170\t                        if len(row) &gt;= 3:\n   171\t                            file_name = row[0]\n   172\t                            ratio = row[1]\n   173\t                            is_valid = row[2]\n   174\t                            existing_data[file_name] = {\n   175\t                                'ratio': ratio,\n   176\t                                'valid': True if is_valid == \&quot;是\&quot; else False\n   177\t                            }\n...\n   200\t        \n   201\t        # 写入CSV文件，明确使用UTF-8编码\n   202\t        try:\n   203\t            with open(SUMMARY_FILE, 'w', newline='', encoding='utf-8') as csvfile:\n   204\t                writer = csv.writer(csvfile)\n   205\t                writer.writerow(['文件名', '经验卡钻检测异常比例(%)', '是否使用经验卡钻检测'])\n   206\t                \n   207\t                for file_name, data in merged_data.items():\n   208\t                    ratio = data.get('ratio', 'N/A')\n   209\t                    is_valid = data.get('valid', False)\n   210\t                    if ratio != 'N/A':\n   211\t                        writer.writerow([file_name, f\&quot;{ratio:.2f}\&quot; if isinstance(ratio, float) else ratio, \&quot;是\&quot; if is_valid else \&quot;否\&quot;])\n   212\t                    else:\n   213\t                        writer.writerow([file_name, \&quot;N/A\&quot;, \&quot;否\&quot;])\n   214\t            \n   215\t            print(f\&quot;异常比例汇总已保存到 {SUMMARY_FILE}\&quot;)\n   216\t            \n   217\t            # 同时保存一份JSON格式的数据，便于其他程序读取\n   218\t            with open(SUMMARY_FILE.replace('.csv', '.json'), 'w', encoding='utf-8') as f:\n   219\t                json.dump(merged_data, f, ensure_ascii=False, indent=2)\n   220\t        except Exception as e:\n   221\t            print(f\&quot;保存汇总文件失败: {e}\&quot;)\n   222\t            # 如果保存失败，尝试使用临时文件名\n   223\t            try:\n   224\t                tmp_file = f\&quot;卡钻异常比例汇总_{int(time.time())}.csv\&quot;\n   225\t                with open(tmp_file, 'w', newline='', encoding='utf-8') as csvfile:\n   226\t                    writer = csv.writer(csvfile)\n   227\t                    writer.writerow(['文件名', '经验卡钻检测异常比例(%)', '是否使用经验卡钻检测'])\n   228\t                    \n   229\t                    for file_name, data in merged_data.items():\n   230\t                        ratio = data.get('ratio', 'N/A')\n   231\t                        is_valid = data.get('valid', False)\n   232\t                        if ratio != 'N/A':\n   233\t                            writer.writerow([file_name, f\&quot;{ratio:.2f}\&quot; if isinstance(ratio, float) else ratio, \&quot;是\&quot; if is_valid else \&quot;否\&quot;])\n   234\t                        else:\n   235\t                            writer.writerow([file_name, \&quot;N/A\&quot;, \&quot;否\&quot;])\n...\n   335\t\n   336\t        attens_energy = np.concatenate(attens_energy, axis=0).reshape(-1)\n   337\t        test_energy = np.array(attens_energy)\n   338\t        test_error_time = time.time() - test_error_start\n   339\t        print(f\&quot;[性能统计] 测试集重建误差计算耗时: {test_error_time:.2f}秒\&quot;)\n   340\t\n   341\t        # 4. 阈值计算\n   342\t        threshold_start = time.time()\n   343\t        combined_energy = np.concatenate([train_energy, test_energy], axis=0)\n   344\t        threshold = np.percentile(combined_energy, 100 - self.args.anomaly_ratio)\n   345\t        print(\&quot;重建误差阈值:\&quot;, threshold)\n   346\t        threshold_time = time.time() - threshold_start\n   347\t        print(f\&quot;[性能统计] 阈值计算耗时: {threshold_time:.2f}秒\&quot;)\n   348\t\n   349\t        # 5. 专业卡钻检测或阈值方法\n   350\t        anomaly_detect_start = time.time()\n   351\t        # 首先尝试使用专业卡钻检测方法\n   352\t        # 直接在代码中设置控制开关，不再依赖环境变量或命令行参数\n   353\t        use_stuck_pipe_detector = True  # 设置为False关闭专业卡钻检测功能，True则启用\n   354\t        \n   355\t        print(f\&quot;专业卡钻检测状态: {'启用' if use_stuck_pipe_detector else '关闭'}\&quot;)\n...\n   387\t                    \n   388\t                    if 0.0001 &lt;= anomaly_ratio &lt;= 0.45:  # 合理的异常比例范围\n   389\t                        print(\&quot;使用经验卡钻检测结果\&quot;)\n   390\t                        pred = anomalies\n   391\t                        test_energy = scores  # 更新异常分数\n   392\t                        # 记录异常比例（使用了经验卡钻检测结果）\n   393\t                        ANOMALY_RATIO_SUMMARY[test_file_base] = {\n   394\t                            'ratio': anomaly_ratio * 100,\n   395\t                            'valid': True\n   396\t                        }\n   397\t                    else:\n   398\t                        print(\&quot;经验卡钻检测异常比例不合理，回退到重建误差方法\&quot;)\n   399\t                        pred = (test_energy &gt; threshold).astype(int)\n   400\t                        # 记录异常比例（未使用经验卡钻检测结果）\n   401\t                        ANOMALY_RATIO_SUMMARY[test_file_base] = {\n   402\t                            'ratio': anomaly_ratio * 100,\n   403\t                            'valid': False\n   404\t                        }\n   405\t                else:\n   406\t                    print(\&quot;没有获取到原始测试数据，使用重建误差方法\&quot;)\n   407\t                    pred = (test_energy &gt; threshold).astype(int)\n   408\t                    # 无经验卡钻检测结果\n   409\t                    ANOMALY_RATIO_SUMMARY[test_file_base] = {\n   410\t                        'ratio': 'N/A',\n   411\t                        'valid': False\n   412\t                    }\n   413\t            except Exception as e:\n   414\t                print(f\&quot;经验卡钻检测失败: {e}，回退到重建误差方法\&quot;)\n   415\t                pred = (test_energy &gt; threshold).astype(int)\n   416\t                # 经验卡钻检测失败\n   417\t                ANOMALY_RATIO_SUMMARY[test_file_base] = {\n   418\t                    'ratio': 'N/A',\n   419\t                    'valid': False\n   420\t                }\n   421\t        else:\n   422\t            # 专业卡钻检测已关闭，直接使用重建误差方法\n   423\t            print(\&quot;专业卡钻检测功能已关闭，使用重建误差方法\&quot;)\n   424\t            pred = (test_energy &gt; threshold).astype(int)\n   425\t            # 专业卡钻检测已关闭\n   426\t            ANOMALY_RATIO_SUMMARY[test_file_base] = {\n   427\t                'ratio': 'N/A',\n   428\t                'valid': False\n   429\t            }\n...\n   483\t        \n   484\t        postprocess_time = time.time() - postprocess_start\n   485\t        print(f\&quot;[性能统计] 后处理和评估耗时: {postprocess_time:.2f}秒\&quot;)\n   486\t\n   487\t        # 7. 结果保存\n   488\t        save_start = time.time()\n   489\t        # (5) 保存结果\n   490\t        batch_size = self.args.seq_len  # 假设 seq_len 是窗口大小\n   491\t        sampled_indices = np.arange(0, len(test_timestamps), batch_size)\n   492\t\n   493\t        # 生成包含时间戳和测试文件名的唯一结果文件名\n   494\t        timestamp_str = time.strftime(\&quot;%Y%m%d-%H%M%S\&quot;)\n   495\t        test_file_name = getattr(self.args, 'test_file', 'default').replace('.npy', '').replace('.', '_')\n   496\t        result_file_name = f'anomaly_results_{test_file_name}_{timestamp_str}.csv'\n   497\t\n   498\t        # 确保pred是整数类型的0和1\n   499\t        pred = pred.astype(int)\n   500\t        \n   501\t        # 创建结果数据框\n   502\t        if no_label_mode:\n   503\t            # 无标签模式下不包含真实标签列\n   504\t            df = pd.DataFrame({\n   505\t                'Timestamp': test_timestamps[sampled_indices],\n   506\t                'Anomaly_Score': test_energy[sampled_indices],\n   507\t                'Predicted_Anomaly': pred[sampled_indices].astype(int)  # 确保是整数类型\n   508\t            })\n   509\t        else:\n   510\t            # 常规模式下包含真实标签列\n   511\t            df = pd.DataFrame({\n   512\t                'Timestamp': test_timestamps[sampled_indices],\n   513\t                'Anomaly_Score': test_energy[sampled_indices],\n   514\t                'Predicted_Anomaly': pred[sampled_indices].astype(int),  # 确保是整数类型\n   515\t                'True_Label': gt[sampled_indices].astype(int)            # 同样确保真实标签也是整数\n   516\t            })\n   517\t            \n   518\t        output_file = os.path.join(folder_path, result_file_name)\n   519\t        df.to_csv(output_file, index=False, encoding='utf-8')\n   520\t        print(f\&quot;预测结果保存至 {output_file}，共 {len(df)} 行\&quot;)\n   521\t        save_time = time.time() - save_start\n   522\t        print(f\&quot;[性能统计] 结果保存耗时: {save_time:.2f}秒\&quot;)\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/data_processing/可视化_特征/处理.py\n     1\timport pandas as pd\n     2\timport numpy as np\n     3\tfrom sklearn.impute import SimpleImputer\n     4\tfrom scipy import stats\n     5\t\n     6\t# 读取CSV文件\n     7\tdf = pd.read_csv(\&quot;泸203H11-4-02205.csv\&quot;)\n     8\t\n     9\t# 1. 填充缺失值\n    10\t# 使用均值填充数值型特征\n    11\tnumeric_features = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP']\n    12\timputer = SimpleImputer(strategy='mean')\n    13\tdf[numeric_features] = imputer.fit_transform(df[numeric_features])\n    14\t\n    15\t# 2. 处理异常值\n    16\t# 使用Z分数方法检测异常值，设定阈值为3\n    17\tfor feature in numeric_features:\n    18\t    z_scores = np.abs(stats.zscore(df[feature]))\n    19\t    df[feature] = np.where(z_scores &gt; 3, np.nan, df[feature])  # 将异常值设为NaN\n    20\t    # 再次填充处理过的NaN值\n    21\t    df[feature] = df[feature].fillna(df[feature].mean())\n    22\t\n    23\t# 保存修正后的数据\n    24\tdf.to_csv(\&quot;cleaned_泸203H11-4-02205.csv\&quot;, index=False)\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/data_processing/异常生成/生成异常时间戳.py\n     1\timport os\n     2\timport pandas as pd\n     3\timport chardet\n     4\t\n     5\t# 输入CSV文件路径\n     6\tinput_file_path = os.path.join('原始', '宁216H2-2(1).csv')\n     7\t\n     8\t# 检测文件编码\n     9\twith open(input_file_path, 'rb') as f:\n    10\t    result = chardet.detect(f.read(10000))\n    11\tencoding = result['encoding']\n    12\t\n    13\t# 读取CSV文件\n    14\tdf = pd.read_csv(input_file_path, encoding=encoding)\n    15\t\n    16\t# 打印DataFrame的列名\n    17\tprint(\&quot;DataFrame的列名：\&quot;, df.columns)\n    18\t\n    19\t# 确保`sfkz`和`date`列存在\n    20\tif 'sfkz' in df.columns and 'date' in df.columns:\n    21\t    # 确保 `date` 列为时间格式\n    22\t    df['date'] = pd.to_datetime(df['date'])\n    23\t\n    24\t    # 初始化变量\n    25\t    in_sequence = False\n    26\t    start_index = 0\n    27\t    sequence_length = 0\n    28\t    sequences = []\n    29\t    high_risk_sequences = []  # 用于保存高风险预警的时间段\n    30\t\n    31\t    # 遍历DataFrame的每一行\n    32\t    for i, row in df.iterrows():\n    33\t        if row['sfkz'] == 1:\n    34\t            if not in_sequence:\n    35\t                in_sequence = True\n    36\t                start_index = i\n    37\t            sequence_length += 1\n    38\t        else:\n    39\t            if in_sequence:\n    40\t                in_sequence = False\n    41\t                end_index = i - 1\n    42\t                start_date = df.loc[start_index, 'date']\n    43\t                end_date = df.loc[end_index, 'date']\n    44\t                # 计算持续时间，时间差值以分钟为单位\n    45\t                duration = (end_date - start_date).total_seconds() / 60  # 持续时间，单位为分钟\n    46\t                sequences.append((sequence_length, start_date, end_date, duration))\n    47\t\n    48\t                # 如果持续时间大于1分钟（30个1值），则添加到高风险预警列表中\n    49\t                if duration &gt; 5:  # 这里的条件是持续时间大于1分钟\n    50\t                    high_risk_sequences.append((sequence_length, start_date, end_date, duration))\n    51\t\n    52\t                sequence_length = 0\n    53\t\n    54\t    # 处理最后的序列\n    55\t    if in_sequence:\n    56\t        end_index = len(df) - 1\n    57\t        start_date = df.loc[start_index, 'date']\n    58\t        end_date = df.loc[end_index, 'date']\n    59\t        duration = (end_date - start_date).total_seconds() / 60  # 持续时间，单位为分钟\n    60\t        sequences.append((sequence_length, start_date, end_date, duration))\n    61\t\n    62\t        # 如果持续时间大于1分钟（30个1值），则添加到高风险预警列表中\n    63\t        if duration &gt; 1:\n    64\t            high_risk_sequences.append((sequence_length, start_date, end_date, duration))\n    65\t\n    66\t    # 输出所有序列的结果\n    67\t    for length, start_date, end_date, duration in sequences:\n    68\t        print(f\&quot;连续1值的个数: {length}, 开始时间戳: {start_date}, 结束时间戳: {end_date}, 持续时间: {duration:.2f} 分钟\&quot;)\n    69\t\n    70\t    # 如果存在高风险预警\n    71\t    if high_risk_sequences:\n    72\t        print(\&quot;\\n高风险预警的时间段：\&quot;)\n    73\t        for length, start_date, end_date, duration in high_risk_sequences:\n    74\t            print(f\&quot;高风险预警 - 连续1值的个数: {length}, 开始时间戳: {start_date}, 结束时间戳: {end_date}, 持续时间: {duration:.2f} 分钟\&quot;)\n    75\t    else:\n    76\t        # 如果没有高风险预警，输出最长的连续1值时间段\n    77\t        if sequences:\n    78\t            max_sequence = max(sequences, key=lambda x: x[0])\n    79\t            length, start_date, end_date, duration = max_sequence\n    80\t            print(\&quot;\\n没有高风险预警\&quot;)\n    81\t            # print(f\&quot;最长连续1值 - 连续1值的个数: {length}, 开始时间戳: {start_date}, 结束时间戳: {end_date}, 持续时间: {duration:.2f} 分钟\&quot;)\n    82\t        else:\n    83\t            print(\&quot;\\n没有连续1值的时间段。\&quot;)\n    84\t\n    85\telse:\n    86\t    print(\&quot;CSV文件中缺少`sfkz`或`date`列\&quot;)\n...\nPath: Time-Series-Library-main-loss/Time-Series-Library-main/data_processing/异常生成/生成时间戳.py\n...\n    59\t\n    60\t# 3. 打印出高风险预警时间（连续5分钟预测为1）\n    61\tdef high_risk_warning(df, time_window_minutes=5):\n    62\t    df['timestamp'] = pd.to_datetime(df['Timestamp'])\n    63\t    high_risk_periods = []\n    64\t    start_idx = None\n    65\t\n    66\t    for i in range(len(df)):\n    67\t        # 如果预测值为1，并且还在同一个高风险段内\n    68\t        if df['Predicted_Anomaly'].iloc[i] == 1:\n    69\t            if start_idx is None:  # 找到一个新的高风险段开始\n    70\t                start_idx = i\n    71\t        else:\n    72\t            if start_idx is not None:  # 结束一个高风险段\n    73\t                duration = (df['timestamp'].iloc[i-1] - df['timestamp'].iloc[start_idx]).total_seconds() / 60\n    74\t                if duration &gt;= time_window_minutes:  # 如果持续时间超过5分钟\n    75\t    ...&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;40e08daa-43ba-43ca-9ee2-db96620c6af6;toolu_vrtx_01UCMagqUc79P6Up6wSmLKDe&quot;:{&quot;requestId&quot;:&quot;40e08daa-43ba-43ca-9ee2-db96620c6af6&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01UCMagqUc79P6Up6wSmLKDe&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;[MEMORY BANK: INITIALIZED]\n\nMemory Bank has been successfully initialized!\n\nCreated files:\n- productContext.md\n- activeContext.md\n- progress.md\n- decisionLog.md\n- systemPatterns.md\n\n\n\n[ATTENTION] Next steps to execute:\n1. Read and update each memory-bank/*.md file\n2. Fill in relevant content following the guidance in each file\n3. Do not use get-memory-bank-info before completing initial edits\n4. After completing edits, you can start using Memory Bank\n\nImportant file descriptions:\n- productContext.md: Define project goals, features, and architecture\n- activeContext.md: Track current work status and focus\n- progress.md: Manage task progress\n- decisionLog.md: Record important decisions\n- systemPatterns.md: Document code patterns and standards\n\nMaintenance Tips:\n- Keep each file under 300 lines for optimal performance\n- Archive old content daily/weekly to memory-bank/archive/\n- Use update-memory-bank tool for detailed maintenance guidance\n- Check file sizes after each work session&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;9f2ef4d5-1457-44f3-8db4-a3f2410e397d;toolu_vrtx_01J2iNwLCy4Cnvi1B95cz3Vn&quot;:{&quot;requestId&quot;:&quot;9f2ef4d5-1457-44f3-8db4-a3f2410e397d&quot;,&quot;toolUseId&quot;:&quot;toolu_vrtx_01J2iNwLCy4Cnvi1B95cz3Vn&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;选择的选项: 重点关注测试执行步骤, 重点关注数据处理流程\n\n❌请记住，不要生成总结性Markdown文档\n❌请记住，不要生成测试脚本\n❌请记住，不要编译，用户自己编译\n❌请记住，不要运行，用户自己运行&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}}},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false,&quot;isAgentConversation&quot;:true,&quot;baselineTimestamp&quot;:0},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;*************-4bcd-8349-06648fda71a6&quot;},&quot;__NEW_AGENT__&quot;:{&quot;id&quot;:&quot;__NEW_AGENT__&quot;,&quot;createdAtIso&quot;:&quot;2025-07-17T13:10:21.629Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-17T13:10:21.714Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false,&quot;isAgentConversation&quot;:true,&quot;baselineTimestamp&quot;:0},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;0b6cca78-9192-46e3-a445-a6341057ffca&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>