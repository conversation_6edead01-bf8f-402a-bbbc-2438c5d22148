# import pandas as pd
# from numpy import amin, amax, zeros, mean
#
#
# # 无量纲化
# def dimensionlessProcessing(df):
#     newDataFrame = pd.DataFrame(index=df.index)
#     columns = df.columns.tolist()
#     for c in columns:
#         d = df[c]
#         MAX = d.max()
#         MIN = d.min()
#         MEAN = d.mean()
#         newDataFrame[c] = ((d - MEAN) / (MAX - MIN)).tolist()
#     return newDataFrame
#
# # 灰度关联算法（输入一个有标准列的多属性df(最后一列为标准列)，返回一个关联数组）
# def GRA_ONE(gray):
#     # 读取为df格式
#     # 无量纲化
#     gray = dimensionlessProcessing(gray)
#
#     std = gray.iloc[:, -1]  # 为标准要素
#     ce = gray.iloc[:, 0:-1]  # 为比较要素
#
#     shape_n, shape_m = ce.shape[0], ce.shape[1]  # 计算行列(此处的列数就是result的列数)
#     # # 与标准要素比较，相减
#     a = zeros([shape_n, shape_m])
#     for i in range(shape_n):
#         for j in range(shape_m):
#             a[i, j] = abs(ce.iloc[i, j] - std[i])
#     #
#     # # 取出矩阵中最大值与最小值
#     c, d = amax(a), amin(a)
#     # # 计算值
#     result = zeros([shape_n, shape_m])
#     for i in range(shape_n):
#             for j in range(shape_m):
#                 result[i, j] = (d + 0.5 * c) / (a[i, j] + 0.5 * c)
#     result_list = []
#     for j in range(shape_m):
#         result_list.append(mean(result[:, j]))
#
#     return result_list
#
#
# # 灰度关联版(获取权重)的指标优选或权重排名 DF为DF数据类型,num为优选个数
# def attribute0pt(DF, num):
#     """
#      此函数可以用于计算一个维度数据样本的属性优选信息
#      输入DF是带有属性值(有name)拼接了采收率，num是优选个数
#      输出 optName优选属性的名称list，optCorr优选属性的相关性list和optName顺序一致，rankCor带有排序的所有数据的属性名+属性相关性系数
#     """
#
#     data =DF
#     # list(DF)可以取出列名(属性名)
#     colName = list(data)
#
#     # 使用灰度关联计算权重
#     corValues = GRA_ONE(data)
#     # 合并成字典 属性名+权重
#     dictCor = dict(zip(colName, corValues))
#     # 按照权重排序，降序
#     rankCor = sorted(dictCor.items(), key=lambda x: x[1], reverse=True)
#
#     # 根据优选任务类型取前3或者前4
#     outCor = rankCor[:num]
#     # 记录优选之后属性名和相关性系数
#     optName = []
#     optCorr = []
#     for i in outCor:
#         # 优选属性名称
#         optName.append(i[0])
#         # 对小数后第三位数进行四舍五入
#         optCorr.append(round(i[1], 3))
#
#     return optName, optCorr, rankCor
#
# if __name__ == '__main__':
#     # 在这里进行函数调用计算灰色关联计划
#     # 示例
#     # 读取excel数据
#     df = pd.read_csv('宁209H8-3钻井日志.csv')
#     # 如果有名字列（无关变量）要提前去除
#     print(df)
#     # 得到灰度关联排名（rankCor）
#     _,_,rankCor=attribute0pt(df,3)
#     print(rankCor)
import pandas as pd
from numpy import amin, amax, zeros, mean

# 无量纲化
def dimensionlessProcessing(df):
    newDataFrame = pd.DataFrame(index=df.index)
    columns = df.columns.tolist()
    for c in columns:
        d = df[c]
        MAX = d.max()
        MIN = d.min()
        MEAN = d.mean()
        if MAX == MIN:  # 避免除以零
            newDataFrame[c] = 0  # 当最大值和最小值相同时，所有值设为0，或根据情况处理
        else:
            newDataFrame[c] = ((d - MEAN) / (MAX - MIN)).tolist()
    return newDataFrame

# 灰度关联算法（输入一个有标准列的多属性df(最后一列为标准列)，返回一个关联数组）
def GRA_ONE(gray):
    # 读取为df格式
    # 无量纲化
    gray = dimensionlessProcessing(gray)

    std = gray.iloc[:, -1]  # 为标准要素
    ce = gray.iloc[:, 0:-1]  # 为比较要素

    shape_n, shape_m = ce.shape[0], ce.shape[1]  # 计算行列(此处的列数就是result的列数)
    # 与标准要素比较，相减
    a = zeros([shape_n, shape_m])
    for i in range(shape_n):
        for j in range(shape_m):
            a[i, j] = abs(ce.iloc[i, j] - std[i])

    # 取出矩阵中最大值与最小值
    c, d = amax(a), amin(a)
    # 计算值
    result = zeros([shape_n, shape_m])
    for i in range(shape_n):
        for j in range(shape_m):
            result[i, j] = (d + 0.5 * c) / (a[i, j] + 0.5 * c)

    result_list = []
    for j in range(shape_m):
        result_list.append(mean(result[:, j]))

    return result_list

# 灰度关联版(获取权重)的指标优选或权重排名 DF为DF数据类型,num为优选个数
def attribute0pt(DF, num):
    """
     此函数可以用于计算一个维度数据样本的属性优选信息
     输入DF是带有属性值(有name)拼接了采收率，num是优选个数
     输出 optName优选属性的名称list，optCorr优选属性的相关性list和optName顺序一致，rankCor带有排序的所有数据的属性名+属性相关性系数
    """
    data = DF
    # list(DF)可以取出列名(属性名)
    colName = list(data)

    # 使用灰度关联计算权重
    corValues = GRA_ONE(data)
    # 合并成字典 属性名+权重
    dictCor = dict(zip(colName, corValues))
    # 按照权重排序，降序
    rankCor = sorted(dictCor.items(), key=lambda x: x[1], reverse=True)

    # 根据优选任务类型取前num个
    outCor = rankCor[:num]
    # 记录优选之后属性名和相关性系数
    optName = []
    optCorr = []
    for i in outCor:
        # 优选属性名称
        optName.append(i[0])
        # 对小数后第三位数进行四舍五入
        optCorr.append(round(i[1], 3))

    return optName, optCorr, rankCor

if __name__ == '__main__':
    # 在这里进行函数调用计算灰色关联计划
    # 示例
    # 读取excel数据
    df = pd.read_csv('宁209H8-3钻井日志shap.csv')

    # 检查是否存在缺失值
    print("缺失值统计：")
    print(df.isnull().sum())

    # 检查每列是否所有值相同
    print("\n检查是否有所有值相同的列：")
    for col in df.columns:
        if df[col].nunique() == 1:
            print(f"列 {col} 中的所有值相同")

    # 填充缺失值（可以根据具体情况调整填充策略）
    df = df.fillna(0)

    # 得到灰度关联排名（rankCor）
    _, _, rankCor = attribute0pt(df, 3)
    print("\n灰度关联排名：")
    print(rankCor)
