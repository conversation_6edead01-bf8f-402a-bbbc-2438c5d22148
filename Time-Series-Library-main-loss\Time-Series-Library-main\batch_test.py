import os
import subprocess
import argparse
import glob
import re
import numpy as np
from sklearn.preprocessing import StandardScaler
import time
import io
import sys
from collections import defaultdict

# ============= 参数设置区域（直接修改这里的值） =============
# 全局控制变量 - 标签模式设置
USE_LABEL = False  # True表示使用标签模式，False表示无标签模式

# 默认参数配置（必填参数在此处设置，无需通过命令行指定）
DEFAULT_CONFIG = {
    "root_path": "./dataset/test",        # 数据根目录
    "data_path": "泸203H11-5",            # 数据子目录
    "model_id": "泸203H11-4",             # 模型ID
    "model": "FEDformer",                 # 模型名称
    "seq_len": 96,                        # 序列长度
    "label_len": 48,                      # 标签长度
    "pred_len": 96,                       # 预测长度
    "test_dir": "./dataset/5051",         # 测试文件目录，不指定则使用root_path/data_path
    "pattern": None,                      # 文件名匹配模式，如 "*.npy"
    "exclude": "*_label.npy,train*.npy"   # 排除的文件模式，用逗号分隔
}
# =========================================================

# 全局缓存，用于存储已加载和处理的数据
CACHE = {
    'scaler': None,           # 训练好的StandardScaler
    'train_data': None,       # 处理后的训练数据
    'train_data_loaded': False # 标记是否已加载训练数据
}

def extract_anomaly_ratio(output_text):
    """从输出文本中提取经验卡钻检测异常比例"""
    # 使用正则表达式查找"经验卡钻检测异常比例: xx.xx%"
    match = re.search(r'经验卡钻检测异常比例: (\d+\.\d+)%', output_text)
    if match:
        return float(match.group(1))
    return None

def ensure_cache_dir():
    """确保缓存目录存在"""
    cache_dir = './.cache'
    if not os.path.exists(cache_dir):
        os.makedirs(cache_dir)
        print(f"创建缓存目录: {cache_dir}")
    return cache_dir

def preprocess_train_data(root_path):
    """预处理训练数据，避免重复加载和处理"""
    if CACHE['train_data_loaded']:
        print("使用缓存的训练数据和标准化器")
        return CACHE['scaler'], CACHE['train_data']
    
    # 确保缓存目录存在
    cache_dir = ensure_cache_dir()
    cache_path = os.path.join(cache_dir, 'train_data_cache.npz')
    
    # 检查是否存在磁盘缓存
    if os.path.exists(cache_path):
        print(f"从磁盘缓存加载训练数据: {cache_path}")
        try:
            cache_data = np.load(cache_path, allow_pickle=True)
            CACHE['train_data'] = cache_data['train_data']
            CACHE['scaler'] = cache_data['scaler'].item()  # 将scaler从数组转回对象
            CACHE['train_data_loaded'] = True
            print("成功从磁盘缓存加载数据")
            return CACHE['scaler'], CACHE['train_data']
        except Exception as e:
            print(f"读取缓存文件失败: {e}，将重新处理训练数据")
    
    print("第一次加载训练数据，进行预处理...")
    start_time = time.time()
    
    # 尝试加载训练数据
    try:
        # 首先尝试从当前根目录加载训练数据
        train_data_path = os.path.join(root_path, "train自201H54-3.npy")
        if not os.path.exists(train_data_path):
            # 如果找不到，则尝试从标准位置加载
            default_train_path = os.path.join("./dataset/test", "train自201H54-3.npy")
            if os.path.exists(default_train_path):
                train_data_path = default_train_path
                print(f"从默认位置加载训练数据: {default_train_path}")
            else:
                raise FileNotFoundError(f"找不到训练数据，已尝试路径: {train_data_path}, {default_train_path}")
            
        train_data = np.load(train_data_path, allow_pickle=True)
        print(f"成功加载训练数据: {train_data_path}")
    except Exception as e:
        print(f"加载训练数据失败: {e}")
        # 创建一个假的训练数据，避免程序崩溃
        train_data = np.zeros((100, 13))
        print("使用空训练数据代替")
    
    # 预处理训练数据
    train_data = train_data[:, :-1]  # 去除最后一列
    scaler = StandardScaler()
    scaler.fit(train_data)
    
    # 存入缓存
    CACHE['scaler'] = scaler
    CACHE['train_data'] = train_data
    CACHE['train_data_loaded'] = True
    
    # 将预处理数据保存到磁盘缓存
    try:
        print(f"保存训练数据到磁盘缓存: {cache_path}")
        np.savez(cache_path, train_data=train_data, scaler=np.array([scaler], dtype=object))
        print("缓存保存成功")
    except Exception as e:
        print(f"保存缓存失败: {e}")
    
    elapsed = time.time() - start_time
    print(f"训练数据预处理完成，耗时: {elapsed:.2f}秒")
    
    return scaler, train_data

def parse_args():
    parser = argparse.ArgumentParser(description='批量异常检测测试')
    # 使用默认参数配置，但允许命令行参数覆盖
    parser.add_argument('--root_path', type=str, default=DEFAULT_CONFIG["root_path"], help='数据根目录')
    parser.add_argument('--model_id', type=str, default=DEFAULT_CONFIG["model_id"], help='模型ID')
    parser.add_argument('--model', type=str, default=DEFAULT_CONFIG["model"], help='模型名称')
    parser.add_argument('--seq_len', type=int, default=DEFAULT_CONFIG["seq_len"], help='序列长度')
    parser.add_argument('--label_len', type=int, default=DEFAULT_CONFIG["label_len"], help='标签长度')
    parser.add_argument('--pred_len', type=int, default=DEFAULT_CONFIG["pred_len"], help='预测长度')
    parser.add_argument('--test_dir', type=str, default=DEFAULT_CONFIG["test_dir"], help='测试文件目录，如不指定则使用root_path/data_path')
    parser.add_argument('--data_path', type=str, default=DEFAULT_CONFIG["data_path"], help='数据子目录')
    parser.add_argument('--pattern', type=str, default=DEFAULT_CONFIG["pattern"], help='文件名匹配模式，例如"*.npy"')
    parser.add_argument('--exclude', type=str, default=DEFAULT_CONFIG["exclude"], help='排除的文件模式，用逗号分隔')
    parser.add_argument('--no_label', action='store_true', help='不使用标签文件')
    parser.add_argument('--save_summary', action='store_true', help='保存汇总结果到CSV文件')
    
    # 如果没有提供命令行参数，使用默认配置
    if len(os.sys.argv) == 1:
        print("使用默认配置运行...")
        print(f"数据目录: {DEFAULT_CONFIG['test_dir'] if DEFAULT_CONFIG['test_dir'] else DEFAULT_CONFIG['root_path'] + '/' + DEFAULT_CONFIG['data_path']}")
        print(f"标签模式: {'' if USE_LABEL else '无'}标签模式")
        args = parser.parse_args([])
    else:
        args = parser.parse_args()
    return args

def find_test_files(test_dir, pattern=None, exclude_patterns=None, no_label=False):
    """
    查找测试文件及其对应的标签文件
    
    参数:
    - test_dir: 测试文件目录
    - pattern: 文件匹配模式（如 "*.npy"）
    - exclude_patterns: 要排除的文件模式列表
    - no_label: 是否不使用标签文件
    
    返回:
    - 包含测试文件和标签文件的字典列表
    """
    if pattern is None:
        pattern = "*.npy"  # 默认查找所有.npy文件
    
    # 转换排除模式为列表
    if exclude_patterns is None:
        exclude_patterns = ["*_label.npy", "train*.npy"]  # 默认排除标签文件和训练文件
    elif isinstance(exclude_patterns, str):
        exclude_patterns = [p.strip() for p in exclude_patterns.split(',')]
    
    # 确保测试目录存在
    if not os.path.exists(test_dir):
        print(f"错误: 测试目录 '{test_dir}' 不存在!")
        return []
        
    # 查找所有符合模式的文件
    all_files = glob.glob(os.path.join(test_dir, pattern))
    
    # 排除匹配排除模式的文件
    for exclude_pattern in exclude_patterns:
        exclude_files = glob.glob(os.path.join(test_dir, exclude_pattern))
        all_files = [f for f in all_files if f not in exclude_files]
    
    test_datasets = []
    
    for test_file in all_files:
        file_name = os.path.basename(test_file)
        file_base, file_ext = os.path.splitext(file_name)
        
        if no_label:
            # 不使用标签文件，直接添加测试文件
            test_datasets.append({
                "name": file_base,
                "test_file": file_name,
                "label_file": "dummy_label.npy"  # 使用虚拟标签文件名
            })
        else:
            # 构建预期的标签文件名
            label_file = os.path.join(test_dir, f"{file_base}_label{file_ext}")
            
            # 检查标签文件是否存在
            if os.path.exists(label_file):
                test_datasets.append({
                    "name": file_base,
                    "test_file": file_name,
                    "label_file": os.path.basename(label_file)
                })
            else:
                print(f"警告: 未找到文件 '{test_file}' 对应的标签文件 '{label_file}'")
    
    return test_datasets

def save_anomaly_summary(anomaly_ratios, filename="卡钻异常比例汇总.csv"):
    """将异常比例汇总保存到CSV文件"""
    import csv
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['文件名', '经验卡钻检测异常比例(%)', '是否使用经验卡钻检测'])
        
        for file_name, data in anomaly_ratios.items():
            ratio = data.get('ratio', 'N/A')
            is_valid = data.get('valid', False)
            if ratio != 'N/A':
                writer.writerow([file_name, f"{ratio:.2f}", "是" if is_valid else "否"])
            else:
                writer.writerow([file_name, "N/A", "否"])
    
    print(f"异常比例汇总已保存到 {filename}")

def main():
    args = parse_args()
    
    # 确保缓存目录存在
    ensure_cache_dir()
    
    # 使用全局控制变量覆盖命令行参数
    args.no_label = not USE_LABEL
    
    # 确定测试目录
    test_dir = args.test_dir if args.test_dir else os.path.join(args.root_path, args.data_path)
    
    # 预处理训练数据，只加载一次
    print("预处理训练数据...")
    scaler, train_data = preprocess_train_data(test_dir)
    
    # 查找测试文件
    test_datasets = find_test_files(test_dir, args.pattern, args.exclude, args.no_label)
    
    if not test_datasets:
        print(f"未在 {test_dir} 中找到有效的测试文件。")
        return
    
    print(f"找到 {len(test_datasets)} 个测试文件:")
    for i, dataset in enumerate(test_datasets):
        if args.no_label:
            print(f"{i+1}. {dataset['name']}: {dataset['test_file']} (无标签模式)")
        else:
            print(f"{i+1}. {dataset['name']}: {dataset['test_file']} -> {dataset['label_file']}")
    
    # 存储每个文件的经验卡钻检测异常比例
    anomaly_ratios = {}
    
    # 批量执行测试
    total_start_time = time.time()
    
    for idx, dataset in enumerate(test_datasets):
        print(f"\n开始测试 ({idx+1}/{len(test_datasets)}): {dataset['name']}")
        test_start_time = time.time()
        
        # 构建测试文件的完整路径 - 使用测试目录中的文件
        test_file_path = os.path.join(test_dir, dataset['test_file'])
        
        cmd = [
            "python", "run.py",
            "--task_name=anomaly_detection",
            "--is_training=0",
            f"--root_path={test_dir}",  # 使用test_dir作为根目录
            f"--data_path={args.data_path}",
            f"--model_id={args.model_id}",
            f"--model={args.model}",
            "--data=anomaly_detection",
            "--features=M",
            f"--seq_len={args.seq_len}",
            f"--label_len={args.label_len}",
            f"--pred_len={args.pred_len}",
            "--e_layers=3",
            "--d_layers=1",
            "--factor=5",
            "--enc_in=12",
            "--dec_in=12",
            "--c_out=12",
            "--d_model=128",
            "--d_ff=512",
            "--des='Exp'",
            "--itr=1",
            "--top_k=5",
            f"--test_file={dataset['test_file']}",  # 只传文件名，不包含路径
        ]
        
        # 根据全局控制变量决定是否使用标签
        if not args.no_label:
            cmd.append(f"--label_file={dataset['label_file']}")
        else:
            cmd.append("--no_label")
        
        # 使用环境变量传递缓存信息，告诉数据加载器使用已处理的数据
        env = os.environ.copy()
        env['USE_CACHED_TRAIN_DATA'] = 'true'
        env['CACHE_DIR'] = './.cache'
        
        # 正确捕获子进程输出 - 使用 Popen 和管道而不是 OutputCapture 类
        try:
#            print(f"执行命令: {' '.join(cmd)}")
            process = subprocess.Popen(
                cmd, 
                env=env, 
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 收集所有输出
            output_lines = []
            for line in process.stdout:
                print(line, end='')  # 实时显示输出
                output_lines.append(line)
            
            # 等待进程完成
            process.wait()
            
            # 全部输出文本
            output_text = ''.join(output_lines)
            
            # 提取异常比例
            anomaly_ratio = extract_anomaly_ratio(output_text)
            
            # 判断是否使用了经验卡钻检测方法
            is_valid_ratio = False
            if anomaly_ratio is not None:
                if "使用经验卡钻检测结果" in output_text:
                    is_valid_ratio = True
                
                # 将结果存入字典
                anomaly_ratios[dataset['name']] = {
                    'ratio': anomaly_ratio, 
                    'valid': is_valid_ratio
                }
                
                print(f"提取到经验卡钻检测异常比例: {anomaly_ratio:.2f}%，{'使用' if is_valid_ratio else '未使用'}经验卡钻检测结果")
            else:
                print("未能提取到经验卡钻检测异常比例")
                anomaly_ratios[dataset['name']] = {'ratio': 'N/A', 'valid': False}
                
        except Exception as e:
            print(f"执行测试命令失败: {e}")
            anomaly_ratios[dataset['name']] = {'ratio': 'N/A', 'valid': False}
        
        test_elapsed = time.time() - test_start_time
        print(f"完成测试: {dataset['name']}, 耗时: {test_elapsed:.2f}秒")
    
    total_elapsed = time.time() - total_start_time
    print(f"\n所有测试完成，总耗时: {total_elapsed:.2f}秒，平均每个测试: {total_elapsed/len(test_datasets):.2f}秒")

    # 打印汇总报告
    print("\n===== 经验卡钻检测异常比例汇总 =====")
    print(f"{'文件名':<30} {'异常比例(%)':<15} {'是否使用'}")
    print("-" * 60)
    
    for file_name, data in anomaly_ratios.items():
        ratio = data.get('ratio', 'N/A')
        is_valid = data.get('valid', False)
        if ratio != 'N/A':
            print(f"{file_name:<30} {ratio:>8.2f}%      {'是' if is_valid else '否'}")
        else:
            print(f"{file_name:<30} {'N/A':<15} 否")
    
    print("-" * 60)
    
    # 保存汇总到CSV文件
    if args.save_summary or True:  # 默认总是保存
        save_anomaly_summary(anomaly_ratios)

if __name__ == "__main__":
    main() 