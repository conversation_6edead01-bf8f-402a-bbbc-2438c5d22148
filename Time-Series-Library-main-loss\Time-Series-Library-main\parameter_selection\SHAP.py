# import pandas as pd
# import numpy as np
# from sklearn.model_selection import train_test_split
# from sklearn.ensemble import RandomForestClassifier
# from sklearn.metrics import accuracy_score
# import shap
#
# # 读取CSV文件
# df = pd.read_csv('宁209H8-3钻井日志.csv')
#
# # 将特征和目标列分开
# X = df.drop(columns=['sfkz'])  # 使用所有剩余的特征
# y = df['sfkz']  # 目标列'sfkz'
#
# # 划分训练集和测试集
# X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
#
# # 使用随机森林训练模型
# rf_model = RandomForestClassifier(n_estimators=100, random_state=42)
# rf_model.fit(X_train, y_train)
#
# # 预测并计算准确率
# y_pred = rf_model.predict(X_test)
# print(f'Accuracy: {accuracy_score(y_test, y_pred)}')
#
# # 获取特征重要性
# feature_importances = rf_model.feature_importances_
# features = X.columns
# importance_df = pd.DataFrame({'Feature': features, 'Importance': feature_importances})
# importance_df = importance_df.sort_values(by='Importance', ascending=False)
#
# print("Feature Importance from Random Forest:")
# print(importance_df)
#
# # 使用SHAP解释模型
# explainer = shap.TreeExplainer(rf_model)
# shap_values = explainer.shap_values(X_test)
#
# # 可视化SHAP值
# shap.summary_plot(shap_values[1], X_test)
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier

# 读取CSV文件
df = pd.read_csv('宁209H8-3钻井日志shap.csv')

# 将特征和目标列分开
X = df.drop(columns=['sfkz'])  # 使用所有剩余的特征
y = df['sfkz']  # 目标列'sfkz'

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 使用随机森林训练模型
rf_model = RandomForestClassifier(n_estimators=100, random_state=42)
rf_model.fit(X_train, y_train)

# 获取特征重要性
feature_importances = rf_model.feature_importances_
features = X.columns

# 创建一个DataFrame来存储特征重要性
importance_df = pd.DataFrame({'Feature': features, 'Importance': feature_importances})
importance_df = importance_df.sort_values(by='Importance', ascending=False)

# 可视化特征重要性
plt.figure(figsize=(10, 6))
sns.barplot(x='Importance', y='Feature', data=importance_df, palette='viridis')
plt.title('Feature Importance Ranked by Random Forest')
plt.xlabel('Importance')
plt.ylabel('Features')
plt.tight_layout()
plt.show()
