import os
import pandas as pd
import chardet

# 输入和输出文件夹路径
input_folder_path = '2024.12.4测试'
output_folder_path = '2024.12.4测试_删除列'

# 指定要保留的列名
columns_to_keep = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP', 'CSIP']

# 创建输出文件夹，如果不存在
os.makedirs(output_folder_path, exist_ok=True)

# 遍历输入文件夹中的所有CSV文件
for filename in os.listdir(input_folder_path):
    if filename.endswith('.csv'):
        input_file_path = os.path.join(input_folder_path, filename)
        output_file_path = os.path.join(output_folder_path, '处理后_' + filename)

        # 检测文件编码
        with open(input_file_path, 'rb') as f:
            result = chardet.detect(f.read(10000))
        encoding = result['encoding']

        # 读取CSV文件并只保留指定的列
        try:
            df = pd.read_csv(input_file_path, usecols=columns_to_keep, encoding=encoding)

            # 将过滤后的数据保存为新的CSV文件
            df.to_csv(output_file_path, index=False)

            print(f"已保存过滤后的文件到 {output_file_path}")
        except Exception as e:
            print(f"处理文件 {input_file_path} 时出错：{e}")

print("所有文件已处理完毕。")
